# Call System Implementation with WebRTC and MQTT 5.0

## Overview

The call system in Hopen provides real-time audio/video communication with **WebRTC** and **MQTT 5.0 signaling** for enhanced performance, supporting screen sharing and Picture-in-Picture (PiP) functionality. It implements a robust state management system for handling incoming calls, media negotiations, and call lifecycle management. The system is built using:

- **WebRTC** for peer-to-peer audio/video communication
- **MQTT 5.0 signaling** with enhanced user properties for call coordination
- **Go call microservice (Gin + WebRTC)** for signaling and call management
- **Enhanced connection quality** with automatic failover mechanisms
- **Valkey** for transient call state storage
- **BLoC pattern** for comprehensive state management with CallBloc
- **Platform-specific PiP APIs** for native integration
- **Platform-specific call handling** for native integration (iOS CallKit, Android ConnectionService)
- **Custom PiP implementation** for web platform

### WebRTC with MQTT 5.0 Benefits

- **Enhanced signaling**: MQTT 5.0 user properties for rich call metadata
- **Reliable message delivery**: QoS guarantees for critical signaling messages
- **Session persistence**: MQTT 5.0 session expiry for mobile optimization
- **Better mobile performance**: Automatic reconnection with exponential backoff
- **Enhanced reliability**: Reason codes for detailed error information
- **Quality optimization**: Dynamic quality adjustment based on network conditions

## Architecture

```
╭─────────────────────────────────────────────────────────────╮
│                   Call System Architecture                  │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │   Call UI   │   │  Call BLoC  │   │    Call     │        │
│  │   (Pages)   │──▶│             │──▶│ Repository  │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│         │                 │                 │               │
│         │                 │                 │               │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │ PiP Manager │   │  Platform   │   │  Services   │        │
│  │             │   │ Call Handler│   │             │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│                                           │                 │
│                                    ╭─────────────╮          │
│                                    │WebRTC       │          │
│                                    │MQTT 5.0     │          │
│                                    │ Go Backend  │          │
│                                    │Platform PiP │          │
│                                    ╰─────────────╯          │
╰─────────────────────────────────────────────────────────────╯
```

## Backend Call Infrastructure

```
╭─────────────────────────────────────────────────────────────╮
│                Go WebRTC Call Backend                       │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │ API Gateway │   │    Call     │   │   Valkey    │        │
│  │             │──▶│Microservice │──▶│   Cache     │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│         │                 │                 │               │
│         │                 │                 │               │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │   Security  │   │  Signaling  │   │ MQTT 5.0    │        │
│  │ Middleware  │   │  Handler    │   │  Events     │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│                                           │                 │
│                                    ╭─────────────╮          │
│                                    │   EMQX      │          │
│                                    │MQTT 5.0     │          │
│                                    │  Broker     │          │
│                                    ╰─────────────╯          │
╰─────────────────────────────────────────────────────────────╯
```

## State Management

### CallBloc

The `CallBloc` orchestrates the entire call lifecycle with comprehensive state management and MQTT 5.0 integration. It maintains the call state and handles events from the UI and the underlying repository. Key responsibilities:

- Register event handlers for user and system actions
- Subscribe to repository event streams
- Manage platform-specific call handling via `PlatformCallHandler`
- Control call duration timer
- Coordinate call state transitions based on events
- Handle MQTT 5.0 signaling messages with user properties

```dart
class CallBloc extends Bloc<CallEvent, CallState> {
  final CallRepositoryExtended _callRepository;
  final UserRepository _userRepository;
  final MqttService _mqttService;
  Timer? _callDurationTimer;
  PlatformCallHandler? _platformCallHandler;

  // Stream subscriptions for repository events
  StreamSubscription<RemoteStreamAdded>? _remoteStreamSubscription;
  StreamSubscription<IceConnectionStateChanged>? _iceConnectionStateSubscription;
  StreamSubscription<CallErrorOccurred>? _callErrorSubscription;
  StreamSubscription<CallOutcomeOccurred>? _callOutcomeSubscription;
  StreamSubscription<JoinCallAcknowledged>? _joinCallAckSubscription;
  StreamSubscription<JoinCallFailed>? _joinCallFailedSubscription;
  StreamSubscription<IncomingOfferOccurred>? _incomingOfferSubscription;
  
  // MQTT 5.0 signaling subscriptions
  StreamSubscription<Map<String, dynamic>>? _signalingSubscription;
  
  // Event handlers, timer management, platform call handler methods
}
```

### CallState

The `CallState` class manages all aspects of a call's lifecycle with enhanced MQTT 5.0 integration:

```dart
class CallState {
  final CallStatus status;  // idle, initiating, joining, dialing, ringing, connecting, active, ended, failed
  final String? callId;
  final bool isCaller;
  
  // Active Call Details
  final bool currentCallIsGroup; // Renamed from activeCallIsGroup for clarity with activeCall... fields
  final String? currentCallGroupId; // ID of the group if currentCallIsGroup is true
  final String? activeCallGroupId; // Populated when a call becomes active, if it's a group call
  final String? activeCallGroupName;
  final String? activeCallGroupAvatarUrl;

  // Media State
  final MediaStream? localStream;
  final Map<String, MediaStream> remoteStreams; // Changed from single remoteStream
  final bool isMuted;
  final bool isVideoEnabled;
  final bool isScreenSharingEnabled;
  final bool isFrontCamera;
  
  // Incoming call details (also used by CallRepoIncomingOfferEvent)
  final String? incomingCallerName;
  final String? incomingCallerAvatar;
  final String? incomingGroupName;
  final String? incomingGroupAvatarUrl;
  final bool incomingCallIsVideoOffered;
  final bool incomingCallIsAudioOffered;
  final bool incomingCallIsScreenShareOffered;
  final Map<String, dynamic>? pendingOfferSdp;
  final String? pendingOfferCallerId;

  // Call Progress
  final DateTime? startTime;
  final Duration callDuration; // Changed from Duration? to Duration.zero default
  
  // Participants
  final List<Participant> participants;
  
  // Error Handling
  final String? errorMessage;
  final String? callEndReason;
  
  // ICE Connection State
  final RTCIceConnectionState? iceConnectionState;
  
  // MQTT 5.0 Signaling State
  final Map<String, String>? signalingProperties;
  final String? sessionId;
}

class Participant {
  final String id;
  final String name;
  final String? avatarUrl;
  final bool isMuted;
  final bool hasVideoEnabled;
  final bool isScreenSharing;
  final bool isLocal; // New field
  final RTCIceConnectionState? connectionState; // New field for individual ICE state
  final Map<String, String>? mqttProperties; // MQTT 5.0 user properties
}
```

### Call Events

The system handles various events through the BLoC pattern with MQTT 5.0 enhancements:

```dart
// User-Initiated Events
InitiateCallEvent // Now includes targetName and MQTT properties
AcceptCallEvent
RejectCallEvent
EndCallEvent
ToggleMuteEvent
ToggleVideoEvent
SwitchCameraEvent
JoinCallEvent // New: To join an existing call (bubbleId, callId, withVideo, withScreenShare, bubbleName)

// Internal Events (from CallRepository or self-dispatch)
ReceiveIncomingCallEvent // Dispatched by IncomingCallListener or CallRepoIncomingOfferEvent
CallRepoRemoteStreamEvent // Now includes participantId and MQTT properties
CallRepoIceStateUpdatedEvent // Now includes participantId
CallRepoErrorEvent
CallRepoOutcomeEvent
UpdateCallDurationInternalEvent
CallRepoJoinAckEvent // New: Signals successful acknowledgment of a join request
CallRepoJoinFailedEvent // New: Signals failure of a join request
CallRepoIncomingOfferEvent // New: Signals an offer from an existing participant when joining

// MQTT 5.0 Signaling Events
MqttSignalingEvent // New: Handles MQTT 5.0 signaling messages
MqttConnectionStateEvent // New: Handles MQTT connection state changes
```

## MQTT 5.0 Signaling Integration

### Call Signaling Topics

The call system uses specific MQTT 5.0 topics for signaling:

```
/call/{callId}/signaling        # WebRTC signaling messages
/call/{callId}/participants     # Participant join/leave events
/call/{callId}/media           # Media state changes
/user/{userId}/call/incoming   # Incoming call notifications
/bubble/{bubbleId}/call/active # Active bubble call status
```

### Enhanced Signaling with User Properties

```dart
// MQTT 5.0 signaling with user properties
Future<void> sendSignalingMessage({
  required String callId,
  required String messageType,
  required Map<String, dynamic> payload,
  Map<String, String>? userProperties,
}) async {
  final topic = '/call/$callId/signaling';
  final message = json.encode(payload);
  
  final properties = {
    'message_type': messageType,
    'call_id': callId,
    'sender_id': currentUserId,
    'timestamp': DateTime.now().toIso8601String(),
    ...?userProperties,
  };
  
  await _mqttService.publishWithProperties(
    topic,
    message,
    properties,
    qos: MqttQos.atLeastOnce,
  );
}

// Subscribe to call signaling
Future<void> subscribeToCallSignaling(String callId) async {
  final topic = '/call/$callId/signaling';
  
  await _mqttService.subscribe(
    topic,
    qos: MqttQos.atLeastOnce,
    userProperties: {
      'subscriber_id': currentUserId,
      'call_id': callId,
    },
  );
}
```

## Platform Call Integration

The call system integrates with platform-specific call APIs (iOS CallKit, Android ConnectionService) via the `PlatformCallHandler` interface:

```dart
class PlatformCallHandler {
  // Report call status changes to the platform
  Future<void> notifyCallStatusChanged(String callId, String status);
  
  // Handle incoming call with MQTT 5.0 properties
  Future<void> handleIncomingCall({
    required String callId,
    required String callerName,
    String? callerAvatar,
    bool hasVideo = false,
    Map<String, String>? mqttProperties,
  });
  
  // Update call with participant information
  Future<void> updateCallParticipants({
    required String callId,
    required List<Participant> participants,
  });
}
```

## WebRTC Service Integration

### Enhanced WebRTC Service

```dart
class WebRtcService {
  final MqttService _mqttService;
  
  // Initialize WebRTC with MQTT 5.0 signaling
  Future<void> initializeWebRTC({
    required String callId,
    required bool isInitiator,
    Map<String, String>? mqttProperties,
  }) async {
    // Setup peer connection
    _peerConnection = await createPeerConnection(_iceServers);
    
    // Subscribe to MQTT signaling
    await _mqttService.subscribe(
      '/call/$callId/signaling',
      qos: MqttQos.atLeastOnce,
    );
    
    // Listen for signaling messages
    _mqttService.messageStream.listen((message) {
      if (message['topic'] == '/call/$callId/signaling') {
        _handleSignalingMessage(message);
      }
    });
  }
  
  // Send WebRTC offer with MQTT 5.0
  Future<void> sendOffer({
    required String callId,
    required RTCSessionDescription offer,
    Map<String, String>? userProperties,
  }) async {
    await _mqttService.publishWithProperties(
      '/call/$callId/signaling',
      json.encode({
        'type': 'offer',
        'sdp': offer.sdp,
      }),
      {
        'message_type': 'offer',
        'call_id': callId,
        'sender_id': currentUserId,
        ...?userProperties,
      },
    );
  }
  
  // Send WebRTC answer with MQTT 5.0
  Future<void> sendAnswer({
    required String callId,
    required RTCSessionDescription answer,
    Map<String, String>? userProperties,
  }) async {
    await _mqttService.publishWithProperties(
      '/call/$callId/signaling',
      json.encode({
        'type': 'answer',
        'sdp': answer.sdp,
      }),
      {
        'message_type': 'answer',
        'call_id': callId,
        'sender_id': currentUserId,
        ...?userProperties,
      },
    );
  }
  
  // Send ICE candidate with MQTT 5.0
  Future<void> sendIceCandidate({
    required String callId,
    required RTCIceCandidate candidate,
    Map<String, String>? userProperties,
  }) async {
    await _mqttService.publishWithProperties(
      '/call/$callId/signaling',
      json.encode({
        'type': 'ice-candidate',
        'candidate': candidate.candidate,
        'sdpMid': candidate.sdpMid,
        'sdpMLineIndex': candidate.sdpMLineIndex,
      }),
      {
        'message_type': 'ice-candidate',
        'call_id': callId,
        'sender_id': currentUserId,
        ...?userProperties,
      },
    );
  }
}
```

## Call Flow with MQTT 5.0

### 1. Initiating a Call

```
User Action → CallBloc → CallRepository → WebRTC Service
                              ├─→ Call Service REST API (initiate call)
                              └─→ WebRTC Setup (prepare media)
```

### 2. Receiving a Call

```
MQTT 5.0 Subscribe → IncomingCallListener → CallBloc → UI Update
                              ├─→ Platform Call Handler (iOS/Android)
                              ├─→ WebRTC Setup (prepare for answer)
                              └─→ Call State Update
```

### 3. WebRTC Signaling Flow

```
Caller                    MQTT 5.0 Broker              Callee
  │                            │                         │
  ├─ Create Offer ─────────────┼────── Offer ──────────▶ │
  │                            │                         │
  │ ◀────── Answer ────────────┼──────── Create Answer ──┤
  │                            │                         │
  ├─ ICE Candidates ───────────┼── ICE Candidates ─────▶ │
  │                            │                         │
  │ ◀── ICE Candidates ────────┼─── ICE Candidates ──────┤
  │                            │                         │
  ├─ Media Stream ─────────────┼─────────────────────────┤
  │        (Direct P2P)        │                         │
```

### 4. Call State Synchronization

```
State Change → CallBloc → MQTT 5.0 Publish → Other Participants
                    ├─→ Local UI Update
                    ├─→ Platform Handler Update
                    └─→ Realtime Service State Sync
```

## Error Handling and Recovery

### MQTT 5.0 Error Handling

```dart
class CallRepository {
  // Handle MQTT connection failures
  void _handleMqttConnectionError(String error) {
    // Attempt reconnection with exponential backoff
    _reconnectMqtt();
    
    // Notify CallBloc of connection issues
    _errorController.add(CallErrorOccurred(
      'MQTT connection failed: $error',
      isRecoverable: true,
    ));
  }
  
  // Handle signaling message failures
  void _handleSignalingError(String callId, String error) {
    // Retry critical signaling messages
    _retrySignalingMessage(callId);
    
    // Fall back to HTTP signaling if MQTT fails
    _fallbackToHttpSignaling(callId);
  }
  
  // Automatic reconnection with session persistence
  Future<void> _reconnectMqtt() async {
    await _mqttService.disconnect();
    
    // Reconnect with session expiry
    await _mqttService.connect(
      clientId: 'call-client-${currentUserId}',
      username: currentUserId,
      password: jwtToken,
      userProperties: {
        'client_type': 'call_service',
        'reconnection': 'true',
      },
    );
    
    // Resubscribe to active call topics
    await _resubscribeToActiveCallTopics();
  }
}
```

### WebRTC Error Recovery

```dart
class WebRtcService {
  // Handle ICE connection failures
  void _handleIceConnectionFailure() {
    // Restart ICE gathering
    _peerConnection?.restartIce();
    
    // Notify about connection issues
    _connectionStateController.add(
      IceConnectionStateChanged(RTCIceConnectionState.RTCIceConnectionStateDisconnected)
    );
  }
  
  // Handle media stream errors
  void _handleMediaStreamError(String error) {
    // Attempt to recreate media stream
    _recreateMediaStream();
    
    // Notify CallBloc of media issues
    _errorController.add(CallErrorOccurred(
      'Media stream error: $error',
      isRecoverable: true,
    ));
  }
}
```

## Performance Optimizations

### MQTT 5.0 Optimizations

- **Session Persistence**: Maintains call state across network changes
- **Message Deduplication**: Prevents duplicate signaling messages
- **QoS Guarantees**: Ensures critical signaling messages are delivered
- **User Properties**: Rich metadata for intelligent message routing
- **Reason Codes**: Detailed error information for debugging

### WebRTC Optimizations

- **Adaptive Bitrate**: Dynamic quality adjustment based on network conditions
- **ICE Restart**: Automatic recovery from connection failures
- **Media Stream Recycling**: Efficient media resource management
- **Connection State Monitoring**: Proactive connection quality management

This enhanced call system provides robust, scalable real-time communication with MQTT 5.0 signaling and comprehensive error handling for the Hopen application.
