/// Notification types supported by the application
enum NotificationType {
  message,
  friendRequest,
  bubbleInvite,
  callIncoming,
  callMissed,
  system,
}

/// Notification priority levels
enum NotificationPriority { low, normal, high, urgent }

/// Notification action types
enum NotificationAction { accept, decline, view, dismiss, reply }

/// Notification data model
class NotificationData {
  const NotificationData({
    required this.id,
    required this.type,
    required this.title,
    required this.body,
    required this.timestamp,
    this.priority = NotificationPriority.normal,
    this.data,
    this.availableActions = const [],
    this.isRead = false,
  });
  final String id;
  final NotificationType type;
  final String title;
  final String body;
  final NotificationPriority priority;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final List<NotificationAction> availableActions;
  final bool isRead;

  NotificationData copyWith({
    String? id,
    NotificationType? type,
    String? title,
    String? body,
    NotificationPriority? priority,
    DateTime? timestamp,
    Map<String, dynamic>? data,
    List<NotificationAction>? availableActions,
    bool? isRead,
  }) => NotificationData(
    id: id ?? this.id,
    type: type ?? this.type,
    title: title ?? this.title,
    body: body ?? this.body,
    priority: priority ?? this.priority,
    timestamp: timestamp ?? this.timestamp,
    data: data ?? this.data,
    availableActions: availableActions ?? this.availableActions,
    isRead: isRead ?? this.isRead,
  );
}

/// Result of notification operations
class NotificationResult {
  const NotificationResult({
    required this.success,
    this.errorMessage,
    this.notificationId,
  });

  factory NotificationResult.success({String? notificationId}) =>
      NotificationResult(success: true, notificationId: notificationId);

  factory NotificationResult.error(String message) =>
      NotificationResult(success: false, errorMessage: message);
  final bool success;
  final String? errorMessage;
  final String? notificationId;
}

/// Abstract interface for notification operations
/// This allows the presentation layer to use notification features
/// without depending on the provider layer implementation
abstract class NotificationService {
  /// Show local notification
  Future<NotificationResult> showLocalNotification(
    NotificationData notification,
  );

  /// Show push notification
  Future<NotificationResult> showPushNotification(
    NotificationData notification,
  );

  /// Cancel notification by ID
  Future<NotificationResult> cancelNotification(String notificationId);

  /// Cancel all notifications
  Future<NotificationResult> cancelAllNotifications();

  /// Get pending notifications
  Future<List<NotificationData>> getPendingNotifications();

  /// Mark notification as read
  Future<NotificationResult> markAsRead(String notificationId);

  /// Mark all notifications as read
  Future<NotificationResult> markAllAsRead();

  /// Handle notification action
  Future<NotificationResult> handleNotificationAction(
    String notificationId,
    NotificationAction action,
    Map<String, dynamic>? actionData,
  );
}

/// Local notification utilities that can be used directly in the business logic layer
class LocalNotificationUtils {
  /// Get icon for notification type
  static String getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.message:
        return 'assets/icons/message.svg';
      case NotificationType.friendRequest:
        return 'assets/icons/person_add.svg';
      case NotificationType.bubbleInvite:
        return 'assets/icons/bubble.svg';
      case NotificationType.callIncoming:
        return 'assets/icons/call_incoming.svg';
      case NotificationType.callMissed:
        return 'assets/icons/call_missed.svg';
      case NotificationType.system:
        return 'assets/icons/system.svg';
    }
  }

  /// Get color for notification type
  static int getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.message:
        return 0xFF2196F3; // Blue
      case NotificationType.friendRequest:
        return 0xFF4CAF50; // Green
      case NotificationType.bubbleInvite:
        return 0xFFFF9800; // Orange
      case NotificationType.callIncoming:
        return 0xFF4CAF50; // Green
      case NotificationType.callMissed:
        return 0xFFF44336; // Red
      case NotificationType.system:
        return 0xFF9C27B0; // Purple
    }
  }

  /// Get priority color
  static int getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return 0xFF9E9E9E; // Grey
      case NotificationPriority.normal:
        return 0xFF2196F3; // Blue
      case NotificationPriority.high:
        return 0xFFFF9800; // Orange
      case NotificationPriority.urgent:
        return 0xFFF44336; // Red
    }
  }

  /// Format notification timestamp
  static String formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// Get action label
  static String getActionLabel(NotificationAction action) {
    switch (action) {
      case NotificationAction.accept:
        return 'Accept';
      case NotificationAction.decline:
        return 'Decline';
      case NotificationAction.view:
        return 'View';
      case NotificationAction.dismiss:
        return 'Dismiss';
      case NotificationAction.reply:
        return 'Reply';
    }
  }

  /// Get action color
  static int getActionColor(NotificationAction action) {
    switch (action) {
      case NotificationAction.accept:
        return 0xFF4CAF50; // Green
      case NotificationAction.decline:
        return 0xFFF44336; // Red
      case NotificationAction.view:
        return 0xFF2196F3; // Blue
      case NotificationAction.dismiss:
        return 0xFF9E9E9E; // Grey
      case NotificationAction.reply:
        return 0xFF2196F3; // Blue
    }
  }

  /// Create notification data for different scenarios
  static NotificationData createMessageNotification({
    required String senderId,
    required String senderName,
    required String message,
    Map<String, dynamic>? additionalData,
  }) => NotificationData(
    id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
    type: NotificationType.message,
    title: senderName,
    body: message,
    timestamp: DateTime.now(),
    data: {'senderId': senderId, 'senderName': senderName, ...?additionalData},
    availableActions: [NotificationAction.view, NotificationAction.reply],
  );

  static NotificationData createFriendRequestNotification({
    required String requesterId,
    required String requesterName,
    Map<String, dynamic>? additionalData,
  }) => NotificationData(
    id: 'friend_req_${DateTime.now().millisecondsSinceEpoch}',
    type: NotificationType.friendRequest,
    title: 'Friend Request',
    body: '$requesterName wants to be your friend',
    timestamp: DateTime.now(),
    data: {
      'requesterId': requesterId,
      'requesterName': requesterName,
      ...?additionalData,
    },
    availableActions: [NotificationAction.accept, NotificationAction.decline],
  );

  static NotificationData createBubbleInviteNotification({
    required String bubbleId,
    required String bubbleName,
    required String inviterName,
    Map<String, dynamic>? additionalData,
  }) => NotificationData(
    id: 'bubble_invite_${DateTime.now().millisecondsSinceEpoch}',
    type: NotificationType.bubbleInvite,
    title: 'Bubble Invitation',
    body: '$inviterName invited you to join "$bubbleName"',
    priority: NotificationPriority.high,
    timestamp: DateTime.now(),
    data: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'inviterName': inviterName,
      ...?additionalData,
    },
    availableActions: [NotificationAction.accept, NotificationAction.decline],
  );

  static NotificationData createIncomingCallNotification({
    required String callerId,
    required String callerName,
    required String callType,
    Map<String, dynamic>? additionalData,
  }) => NotificationData(
    id: 'call_incoming_${DateTime.now().millisecondsSinceEpoch}',
    type: NotificationType.callIncoming,
    title: 'Incoming call',
    body: '$callerName is calling ($callType)',
    priority: NotificationPriority.urgent,
    timestamp: DateTime.now(),
    data: {
      'callerId': callerId,
      'callerName': callerName,
      'callType': callType,
      ...?additionalData,
    },
    availableActions: [NotificationAction.accept, NotificationAction.decline],
  );
}
