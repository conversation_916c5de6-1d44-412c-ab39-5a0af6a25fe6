import 'dart:io';
import 'package:dio/dio.dart';
import 'exceptions.dart';
import 'result.dart';
import '../services/logging_service.dart';

/// Standardized error handler for all request types
/// Provides consistent error handling patterns across contact, bubble, and friend requests
class RequestErrorHandler {
  /// Handle errors from HTTP requests and convert to standardized Result
  static Result<T> handleHttpError<T>(dynamic error, String operation) {
    LoggingService.error('RequestErrorHandler: $operation failed with error: $error');
    
    if (error is DioException) {
      return _handleDioError<T>(error, operation);
    }
    
    if (error is SocketException) {
      return Result.failure(NetworkException(
        message: 'Network connection failed during $operation',
        originalError: error,
      ));
    }
    
    if (error is FormatException) {
      return Result.failure(DataParsingException(
        message: 'Invalid data format received during $operation',
        originalError: error,
      ));
    }
    
    // Generic error handling
    return Result.failure(UnknownException(
      message: 'Unexpected error during $operation: ${error.toString()}',
      originalError: error,
    ));
  }
  
  /// Handle Dio-specific errors
  static Result<T> _handleDioError<T>(DioException error, String operation) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Result.failure(NetworkException(
          message: 'Request timeout during $operation',
          originalError: error,
        ));
        
      case DioExceptionType.badResponse:
        return _handleBadResponse<T>(error, operation);
        
      case DioExceptionType.cancel:
        return Result.failure(NetworkException(
          message: 'Request was cancelled during $operation',
          originalError: error,
        ));
        
      case DioExceptionType.connectionError:
        return Result.failure(NetworkException(
          message: 'Connection error during $operation',
          originalError: error,
        ));
        
      case DioExceptionType.badCertificate:
        return Result.failure(NetworkException(
          message: 'SSL certificate error during $operation',
          originalError: error,
        ));
        
      case DioExceptionType.unknown:
      default:
        return Result.failure(NetworkException(
          message: 'Unknown network error during $operation',
          originalError: error,
        ));
    }
  }
  
  /// Handle bad HTTP response errors
  static Result<T> _handleBadResponse<T>(DioException error, String operation) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;
    
    String errorMessage = 'HTTP $statusCode error during $operation';
    
    // Extract error message from response if available
    if (responseData is Map<String, dynamic>) {
      if (responseData.containsKey('error')) {
        errorMessage = responseData['error'].toString();
      } else if (responseData.containsKey('message')) {
        errorMessage = responseData['message'].toString();
      }
    }
    
    switch (statusCode) {
      case 400:
        return Result.failure(ValidationException(
          message: errorMessage,
          originalError: error,
        ));
        
      case 401:
        return Result.failure(AuthenticationException(
          message: 'Authentication failed during $operation',
          originalError: error,
        ));
        
      case 403:
        return Result.failure(AuthorizationException(
          message: 'Access denied during $operation',
          originalError: error,
        ));
        
      case 404:
        return Result.failure(NotFoundException(
          message: 'Resource not found during $operation',
          originalError: error,
        ));
        
      case 409:
        return Result.failure(ConflictException(
          message: errorMessage,
          originalError: error,
        ));
        
      case 429:
        return Result.failure(RateLimitException(
          message: 'Rate limit exceeded during $operation',
          originalError: error,
        ));
        
      case 500:
      case 502:
      case 503:
      case 504:
        return Result.failure(ServerException(
          message: 'Server error during $operation',
          originalError: error,
        ));
        
      default:
        return Result.failure(NetworkException(
          message: errorMessage,
          originalError: error,
        ));
    }
  }
  
  /// Validate request parameters before making API calls
  static Result<void> validateRequestParameters({
    required String operation,
    required Map<String, dynamic> parameters,
  }) {
    final errors = <String>[];
    
    for (final entry in parameters.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value == null || (value is String && value.isEmpty)) {
        errors.add('$key is required for $operation');
      }
      
      // Validate UUID format for ID fields
      if (key.toLowerCase().contains('id') && value is String) {
        if (!_isValidUuid(value)) {
          errors.add('$key must be a valid UUID for $operation');
        }
      }
    }
    
    if (errors.isNotEmpty) {
      return Result.failure(ValidationException(
        message: 'Validation failed for $operation: ${errors.join(', ')}',
      ));
    }
    
    return Result.success(null);
  }
  
  /// Check if a string is a valid UUID
  static bool _isValidUuid(String value) {
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    );
    return uuidRegex.hasMatch(value);
  }
  
  /// Create user-friendly error messages for UI display
  static String getUserFriendlyMessage(AppException exception) {
    switch (exception.runtimeType) {
      case NetworkException:
        return 'Please check your internet connection and try again.';
        
      case ValidationException:
        return exception.userMessage ?? 'Please check your input and try again.';
        
      case AuthenticationException:
        return 'Please sign in again to continue.';
        
      case AuthorizationException:
        return 'You don\'t have permission to perform this action.';
        
      case NotFoundException:
        return 'The requested item could not be found.';
        
      case ConflictException:
        return exception.userMessage ?? 'This action conflicts with existing data.';
        
      case RateLimitException:
        return 'Too many requests. Please wait a moment and try again.';
        
      case ServerException:
        return 'Server is temporarily unavailable. Please try again later.';
        
      default:
        return 'Something went wrong. Please try again.';
    }
  }
}
