import 'package:equatable/equatable.dart';
import 'package:hopen/statefulbusinesslogic/core/models/user_privacy_settings_model.dart';

/// Events for PrivacySettingsBloc
/// Follows the clean architecture pattern - business logic layer
abstract class PrivacySettingsEvent extends Equatable {
  const PrivacySettingsEvent();

  @override
  List<Object?> get props => [];
}

/// Load privacy settings for a user
class LoadPrivacySettings extends PrivacySettingsEvent {
  final String userId;

  const LoadPrivacySettings(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Update account privacy setting
class UpdateAccountPrivacy extends PrivacySettingsEvent {
  final String userId;
  final bool isPrivate;

  const UpdateAccountPrivacy({required this.userId, required this.isPrivate});

  @override
  List<Object?> get props => [userId, isPrivate];
}

/// Update activity status visibility
class UpdateActivityStatusVisibility extends PrivacySettingsEvent {
  final String userId;
  final ProfileFieldVisibility visibility;

  const UpdateActivityStatusVisibility({required this.userId, required this.visibility});

  @override
  List<Object?> get props => [userId, visibility];
}

/// Update email visibility
class UpdateEmailVisibility extends PrivacySettingsEvent {
  final String userId;
  final ProfileFieldVisibility visibility;

  const UpdateEmailVisibility({required this.userId, required this.visibility});

  @override
  List<Object?> get props => [userId, visibility];
}

/// Update birthday visibility
class UpdateBirthdayVisibility extends PrivacySettingsEvent {
  final String userId;
  final ProfileFieldVisibility visibility;

  const UpdateBirthdayVisibility({required this.userId, required this.visibility});

  @override
  List<Object?> get props => [userId, visibility];
}

/// Update contact request visibility
class UpdateContactRequestVisibility extends PrivacySettingsEvent {
  final String userId;
  final ProfileFieldVisibility visibility;

  const UpdateContactRequestVisibility({required this.userId, required this.visibility});

  @override
  List<Object?> get props => [userId, visibility];
}

/// Block a user
class BlockUser extends PrivacySettingsEvent {
  final String userId;
  final String targetUserId;

  const BlockUser({required this.userId, required this.targetUserId});

  @override
  List<Object?> get props => [userId, targetUserId];
}

/// Unblock a user
class UnblockUser extends PrivacySettingsEvent {
  final String userId;
  final String targetUserId;

  const UnblockUser({required this.userId, required this.targetUserId});

  @override
  List<Object?> get props => [userId, targetUserId];
}

/// Load blocked users list
class LoadBlockedUsers extends PrivacySettingsEvent {
  final String userId;

  const LoadBlockedUsers(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Update complete privacy settings
class UpdatePrivacySettings extends PrivacySettingsEvent {
  final UserPrivacySettingsModel settings;

  const UpdatePrivacySettings(this.settings);

  @override
  List<Object?> get props => [settings];
} 