import 'package:equatable/equatable.dart';
import 'package:hopen/statefulbusinesslogic/core/models/user_privacy_settings_model.dart';

/// States for PrivacySettingsBloc
/// Follows the clean architecture pattern - business logic layer
abstract class PrivacySettingsState extends Equatable {
  const PrivacySettingsState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class PrivacySettingsInitial extends PrivacySettingsState {}

/// Loading privacy settings
class PrivacySettingsLoading extends PrivacySettingsState {}

/// Privacy settings loaded successfully
class PrivacySettingsLoaded extends PrivacySettingsState {
  final UserPrivacySettingsModel settings;
  final List<String> blockedUsers;

  const PrivacySettingsLoaded({
    required this.settings,
    required this.blockedUsers,
  });

  @override
  List<Object?> get props => [settings, blockedUsers];
}

/// Privacy settings updated successfully
class PrivacySettingsUpdated extends PrivacySettingsState {
  final UserPrivacySettingsModel settings;
  final String? message;

  const PrivacySettingsUpdated({
    required this.settings,
    this.message,
  });

  @override
  List<Object?> get props => [settings, message];
}

/// User blocked successfully
class UserBlocked extends PrivacySettingsState {
  final String targetUserId;
  final String message;

  const UserBlocked({
    required this.targetUserId,
    required this.message,
  });

  @override
  List<Object?> get props => [targetUserId, message];
}

/// User unblocked successfully
class UserUnblocked extends PrivacySettingsState {
  final String targetUserId;
  final String message;

  const UserUnblocked({
    required this.targetUserId,
    required this.message,
  });

  @override
  List<Object?> get props => [targetUserId, message];
}

/// Blocked users loaded successfully
class BlockedUsersLoaded extends PrivacySettingsState {
  final List<String> blockedUsers;

  const BlockedUsersLoaded(this.blockedUsers);

  @override
  List<Object?> get props => [blockedUsers];
}

/// Error state
class PrivacySettingsError extends PrivacySettingsState {
  final String message;

  const PrivacySettingsError(this.message);

  @override
  List<Object?> get props => [message];
} 