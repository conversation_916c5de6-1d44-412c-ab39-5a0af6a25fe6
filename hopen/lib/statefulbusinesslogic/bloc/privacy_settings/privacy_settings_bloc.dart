import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hopen/repositories/privacy_settings/privacy_settings_repository.dart';
import 'package:hopen/statefulbusinesslogic/bloc/privacy_settings/privacy_settings_event.dart';
import 'package:hopen/statefulbusinesslogic/bloc/privacy_settings/privacy_settings_state.dart';
import 'package:hopen/statefulbusinesslogic/core/models/user_privacy_settings_model.dart';
import 'package:hopen/statefulbusinesslogic/core/services/logging_service.dart';

/// BLoC for managing privacy settings
/// Follows the clean architecture pattern - business logic layer
class PrivacySettingsBloc extends Bloc<PrivacySettingsEvent, PrivacySettingsState> {
  final PrivacySettingsRepository _repository;

  PrivacySettingsBloc(this._repository) : super(PrivacySettingsInitial()) {
    on<LoadPrivacySettings>(_onLoadPrivacySettings);
    on<UpdateAccountPrivacy>(_onUpdateAccountPrivacy);
    on<UpdateActivityStatusVisibility>(_onUpdateActivityStatusVisibility);
    on<UpdateEmailVisibility>(_onUpdateEmailVisibility);
    on<UpdateBirthdayVisibility>(_onUpdateBirthdayVisibility);
    on<UpdateContactRequestVisibility>(_onUpdateContactRequestVisibility);
    on<BlockUser>(_onBlockUser);
    on<UnblockUser>(_onUnblockUser);
    on<LoadBlockedUsers>(_onLoadBlockedUsers);
    on<UpdatePrivacySettings>(_onUpdatePrivacySettings);
  }

  Future<void> _onLoadPrivacySettings(
    LoadPrivacySettings event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      emit(PrivacySettingsLoading());
      
      final settingsResult = await _repository.loadPrivacySettings(event.userId);
      final blockedUsersResult = await _repository.getBlockedUsers(event.userId);

      if (settingsResult.isSuccess && blockedUsersResult.isSuccess) {
        emit(PrivacySettingsLoaded(
          settings: settingsResult.data,
          blockedUsers: blockedUsersResult.data,
        ));
      } else {
        final errorMessage = settingsResult.isFailure 
            ? settingsResult.error.userMessage 
            : blockedUsersResult.error.userMessage;
        emit(PrivacySettingsError(errorMessage));
      }
    } catch (e) {
      LoggingService.error('Error loading privacy settings: $e');
      emit(const PrivacySettingsError('Failed to load privacy settings'));
    }
  }

  Future<void> _onUpdateAccountPrivacy(
    UpdateAccountPrivacy event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is PrivacySettingsLoaded) {
        final updatedSettings = currentState.settings.copyWith(
          whoCanSendContactRequests: event.isPrivate 
              ? ProfileFieldVisibility.contactsOnly 
              : ProfileFieldVisibility.everyone,
        );

        final result = await _repository.updatePrivacySettings(updatedSettings);
        
        if (result.isSuccess) {
          emit(PrivacySettingsUpdated(
            settings: result.data,
            message: 'Account privacy updated successfully',
          ));
        } else {
          emit(PrivacySettingsError(result.error.userMessage));
        }
      }
    } catch (e) {
      LoggingService.error('Error updating account privacy: $e');
      emit(const PrivacySettingsError('Failed to update account privacy'));
    }
  }

  Future<void> _onUpdateActivityStatusVisibility(
    UpdateActivityStatusVisibility event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is PrivacySettingsLoaded) {
        final updatedSettings = currentState.settings.copyWith(
          onlineStatusVisibility: event.visibility,
        );

        final result = await _repository.updatePrivacySettings(updatedSettings);
        
        if (result.isSuccess) {
          emit(PrivacySettingsUpdated(
            settings: result.data,
            message: 'Activity status visibility updated',
          ));
        } else {
          emit(PrivacySettingsError(result.error.userMessage));
        }
      }
    } catch (e) {
      LoggingService.error('Error updating activity status visibility: $e');
      emit(const PrivacySettingsError('Failed to update activity status visibility'));
    }
  }

  Future<void> _onUpdateEmailVisibility(
    UpdateEmailVisibility event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is PrivacySettingsLoaded) {
        final updatedSettings = currentState.settings.copyWith(
          emailVisibility: event.visibility,
        );

        final result = await _repository.updatePrivacySettings(updatedSettings);
        
        if (result.isSuccess) {
          emit(PrivacySettingsUpdated(
            settings: result.data,
            message: 'Email visibility updated',
          ));
        } else {
          emit(PrivacySettingsError(result.error.userMessage));
        }
      }
    } catch (e) {
      LoggingService.error('Error updating email visibility: $e');
      emit(const PrivacySettingsError('Failed to update email visibility'));
    }
  }

  Future<void> _onUpdateBirthdayVisibility(
    UpdateBirthdayVisibility event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is PrivacySettingsLoaded) {
        final updatedSettings = currentState.settings.copyWith(
          birthdayVisibility: event.visibility,
        );

        final result = await _repository.updatePrivacySettings(updatedSettings);
        
        if (result.isSuccess) {
          emit(PrivacySettingsUpdated(
            settings: result.data,
            message: 'Birthday visibility updated',
          ));
        } else {
          emit(PrivacySettingsError(result.error.userMessage));
        }
      }
    } catch (e) {
      LoggingService.error('Error updating birthday visibility: $e');
      emit(const PrivacySettingsError('Failed to update birthday visibility'));
    }
  }

  Future<void> _onUpdateContactRequestVisibility(
    UpdateContactRequestVisibility event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is PrivacySettingsLoaded) {
        final updatedSettings = currentState.settings.copyWith(
          whoCanSendContactRequests: event.visibility,
        );

        final result = await _repository.updatePrivacySettings(updatedSettings);
        
        if (result.isSuccess) {
          emit(PrivacySettingsUpdated(
            settings: result.data,
            message: 'Contact request visibility updated',
          ));
        } else {
          emit(PrivacySettingsError(result.error.userMessage));
        }
      }
    } catch (e) {
      LoggingService.error('Error updating contact request visibility: $e');
      emit(const PrivacySettingsError('Failed to update contact request visibility'));
    }
  }

  Future<void> _onBlockUser(
    BlockUser event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final result = await _repository.blockUser(
        userId: event.userId,
        targetUserId: event.targetUserId,
      );

      if (result.isSuccess) {
        emit(UserBlocked(
          targetUserId: event.targetUserId,
          message: 'User blocked successfully',
        ));
        
        // Reload blocked users list
        add(LoadBlockedUsers(event.userId));
      } else {
        emit(PrivacySettingsError(result.error.userMessage));
      }
    } catch (e) {
      LoggingService.error('Error blocking user: $e');
      emit(const PrivacySettingsError('Failed to block user'));
    }
  }

  Future<void> _onUnblockUser(
    UnblockUser event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final result = await _repository.unblockUser(
        userId: event.userId,
        targetUserId: event.targetUserId,
      );

      if (result.isSuccess) {
        emit(UserUnblocked(
          targetUserId: event.targetUserId,
          message: 'User unblocked successfully',
        ));
        
        // Reload blocked users list
        add(LoadBlockedUsers(event.userId));
      } else {
        emit(PrivacySettingsError(result.error.userMessage));
      }
    } catch (e) {
      LoggingService.error('Error unblocking user: $e');
      emit(const PrivacySettingsError('Failed to unblock user'));
    }
  }

  Future<void> _onLoadBlockedUsers(
    LoadBlockedUsers event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final result = await _repository.getBlockedUsers(event.userId);

      if (result.isSuccess) {
        emit(BlockedUsersLoaded(result.data));
      } else {
        emit(PrivacySettingsError(result.error.userMessage));
      }
    } catch (e) {
      LoggingService.error('Error loading blocked users: $e');
      emit(const PrivacySettingsError('Failed to load blocked users'));
    }
  }

  Future<void> _onUpdatePrivacySettings(
    UpdatePrivacySettings event,
    Emitter<PrivacySettingsState> emit,
  ) async {
    try {
      final result = await _repository.updatePrivacySettings(event.settings);

      if (result.isSuccess) {
        emit(PrivacySettingsUpdated(
          settings: result.data,
          message: 'Privacy settings updated successfully',
        ));
      } else {
        emit(PrivacySettingsError(result.error.userMessage));
      }
    } catch (e) {
      LoggingService.error('Error updating privacy settings: $e');
      emit(const PrivacySettingsError('Failed to update privacy settings'));
    }
  }
} 