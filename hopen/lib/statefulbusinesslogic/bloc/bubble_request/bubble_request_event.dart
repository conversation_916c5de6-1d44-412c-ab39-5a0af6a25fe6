import 'package:equatable/equatable.dart';

abstract class BubbleRequest<PERSON>vent extends Equatable {
  const BubbleRequestEvent();

  @override
  List<Object?> get props => [];
}

class LoadBubbleRequestEvent extends BubbleRequestEvent {
  const LoadBubbleRequestEvent({
    required this.requestId,
    required this.bubbleId,
    required this.requestType,
    required this.requesterId,
    required this.requesterName,
    this.targetUserId,
    this.targetUserName,
    this.message,
  });

  final String requestId;
  final String bubbleId;
  final String requestType;
  final String requesterId;
  final String requesterName;
  final String? targetUserId;
  final String? targetUserName;
  final String? message;

  @override
  List<Object?> get props => [
    requestId,
    bubbleId,
    requestType,
    requesterId,
    requesterName,
    targetUserId,
    targetUserName,
    message,
  ];
}

class AcceptBubbleRequestEvent extends BubbleRequestEvent {
  const AcceptBubbleRequestEvent({
    required this.requestId,
    required this.bubbleId,
    required this.userId,
  });

  final String requestId;
  final String bubbleId;
  final String userId;

  @override
  List<Object> get props => [requestId, bubbleId, userId];
}

class DeclineBubbleRequestEvent extends BubbleRequestEvent {
  const DeclineBubbleRequestEvent({
    required this.requestId,
    required this.bubbleId,
    required this.userId,
  });

  final String requestId;
  final String bubbleId;
  final String userId;

  @override
  List<Object> get props => [requestId, bubbleId, userId];
}

class ResetBubbleRequestEvent extends BubbleRequestEvent {
  const ResetBubbleRequestEvent();
}