import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../repositories/bubble/bubble_repository.dart';
import 'bubble_request_event.dart';
import 'bubble_request_state.dart';

class BubbleRequestBloc extends Bloc<BubbleRequestEvent, BubbleRequestState> {
  BubbleRequestBloc({required BubbleRepository bubbleRepository})
      : _bubbleRepository = bubbleRepository,
        super(const BubbleRequestState()) {
    on<LoadBubbleRequestEvent>(_onLoadBubbleRequest);
    on<AcceptBubbleRequestEvent>(_onAcceptBubbleRequest);
    on<DeclineBubbleRequestEvent>(_onDeclineBubbleRequest);
    on<ResetBubbleRequestEvent>(_onResetBubbleRequest);
  }

  final BubbleRepository _bubbleRepository;

  Future<void> _onAcceptBubbleRequest(
    AcceptBubbleRequestEvent event,
    Emitter<BubbleRequestState> emit,
  ) async {
    emit(state.copyWith(status: BubbleRequestStatus.accepting));
    
    try {
      // Implement actual API call to accept bubble request
      final result = await _bubbleRepository.acceptBubbleRequest(
        requestId: event.requestId,
        bubbleId: event.bubbleId,
        userId: event.userId,
      );

      if (result.isSuccess) {
        emit(state.copyWith(status: BubbleRequestStatus.accepted));
      } else {
        emit(state.copyWith(
          status: BubbleRequestStatus.error,
          errorMessage: result.error?.userMessage ?? 'Failed to accept bubble request',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: BubbleRequestStatus.error,
        errorMessage: 'Failed to accept bubble request: ${e.toString()}',
      ));
    }
  }

  Future<void> _onDeclineBubbleRequest(
    DeclineBubbleRequestEvent event,
    Emitter<BubbleRequestState> emit,
  ) async {
    emit(state.copyWith(status: BubbleRequestStatus.declining));
    
    try {
      // Implement actual API call to decline bubble request
      final result = await _bubbleRepository.declineBubbleRequest(
        requestId: event.requestId,
        bubbleId: event.bubbleId,
        userId: event.userId,
      );

      if (result.isSuccess) {
        emit(state.copyWith(status: BubbleRequestStatus.declined));
      } else {
        emit(state.copyWith(
          status: BubbleRequestStatus.error,
          errorMessage: result.error?.userMessage ?? 'Failed to decline bubble request',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: BubbleRequestStatus.error,
        errorMessage: 'Failed to decline bubble request: ${e.toString()}',
      ));
    }
  }

  void _onLoadBubbleRequest(
    LoadBubbleRequestEvent event,
    Emitter<BubbleRequestState> emit,
  ) {
    emit(state.copyWith(
      status: BubbleRequestStatus.loaded,
      requestId: event.requestId,
      bubbleId: event.bubbleId,
      requestType: event.requestType,
      requesterId: event.requesterId,
      requesterName: event.requesterName,
      targetUserId: event.targetUserId,
      targetUserName: event.targetUserName,
      message: event.message,
    ));
  }

  void _onResetBubbleRequest(
    ResetBubbleRequestEvent event,
    Emitter<BubbleRequestState> emit,
  ) {
    emit(const BubbleRequestState());
  }
}
