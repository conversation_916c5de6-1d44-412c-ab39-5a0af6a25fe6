import 'package:equatable/equatable.dart';

enum BubbleRequestStatus {
  initial,
  loading,
  loaded,
  accepting,
  accepted,
  declining,
  declined,
  error,
}

class BubbleRequestState extends Equatable {
  const BubbleRequestState({
    this.status = BubbleRequestStatus.initial,
    this.errorMessage,
    this.requestId = '',
    this.bubbleId = '',
    this.requestType = '',
    this.requesterId = '',
    this.requesterName = '',
    this.targetUserId = '',
    this.targetUserName = '',
    this.message,
  });

  final BubbleRequestStatus status;
  final String? errorMessage;
  final String requestId;
  final String bubbleId;
  final String requestType;
  final String requesterId;
  final String requesterName;
  final String targetUserId;
  final String targetUserName;
  final String? message;

  BubbleRequestState copyWith({
    BubbleRequestStatus? status,
    String? errorMessage,
    String? requestId,
    String? bubbleId,
    String? requestType,
    String? requesterId,
    String? requesterName,
    String? targetUserId,
    String? targetUserName,
    String? message,
  }) {
    return BubbleRequestState(
      status: status ?? this.status,
      errorMessage: errorMessage,
      requestId: requestId ?? this.requestId,
      bubbleId: bubbleId ?? this.bubbleId,
      requestType: requestType ?? this.requestType,
      requesterId: requesterId ?? this.requesterId,
      requesterName: requesterName ?? this.requesterName,
      targetUserId: targetUserId ?? this.targetUserId,
      targetUserName: targetUserName ?? this.targetUserName,
      message: message ?? this.message,
    );
  }

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    requestId,
    bubbleId,
    requestType,
    requesterId,
    requesterName,
    targetUserId,
    targetUserName,
    message,
  ];
}