import '../../repositories/friendship/friend_request_repository.dart';
import '../../repositories/base/base_request_repository.dart';
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/friend_request.dart';
import '../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../statefulbusinesslogic/core/error/request_error_handler.dart';
import '../datasources/http_remote_datasource.dart';

class FriendRequestRepositoryImpl extends BaseRequestRepository<FriendRequest>
    implements FriendRequestRepository {
  const FriendRequestRepositoryImpl({
    required HttpRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  final HttpRemoteDataSource _remoteDataSource;

  @override
  Future<Result<List<FriendRequest>>> getPendingRequests(String userId) async {
    return handleListRepositoryOperation<FriendRequest>(
      'get pending friend requests',
      () async {
        logOperation('getPendingFriendRequests', {'userId': userId});
        final response = await _remoteDataSource.get('/friendship/requests');

        final requests = <FriendRequest>[];
        for (final requestData in response['requests'] ?? []) {
          requests.add(FriendRequest.fromJson(requestData));
        }

        return requests;
      },
      validationParams: createValidationParams(userId: userId),
    );
  }

  @override
  Future<Result<List<FriendRequest>>> getPendingFriendRequests() async {
    // For backward compatibility, use empty userId
    return getPendingRequests('');
  }

  @override
  Future<Result<void>> acceptRequest(String requestId) async {
    return handleVoidRepositoryOperation(
      'accept friend request',
      () async {
        logOperation('acceptFriendRequest', {'requestId': requestId});
        await _remoteDataSource.post('/friendship/requests/$requestId/accept', {});
      },
      validationParams: createValidationParams(requestId: requestId),
    );
  }

  @override
  Future<Result<void>> acceptFriendRequest(String requestId) async {
    // For backward compatibility
    return acceptRequest(requestId);
  }

  @override
  Future<Result<void>> declineRequest(String requestId) async {
    return handleVoidRepositoryOperation(
      'decline friend request',
      () async {
        logOperation('declineFriendRequest', {'requestId': requestId});
        await _remoteDataSource.post('/friendship/requests/$requestId/decline', {});
      },
      validationParams: createValidationParams(requestId: requestId),
    );
  }

  @override
  Future<Result<void>> declineFriendRequest(String requestId) async {
    // For backward compatibility
    return declineRequest(requestId);
  }

  @override
  Future<Result<FriendRequest>> getRequest(String requestId) async {
    return handleRepositoryOperation<FriendRequest>(
      'get friend request',
      () async {
        logOperation('getFriendRequest', {'requestId': requestId});
        final response = await _remoteDataSource.get('/friendship/requests/$requestId');
        return FriendRequest.fromJson(response['request']);
      },
      validationParams: createValidationParams(requestId: requestId),
    );
  }

  @override
  Future<Result<FriendRequest>> getFriendRequest(String requestId) async {
    // For backward compatibility
    return getRequest(requestId);
  }

  @override
  Future<Result<List<String>>> getFriends() async {
    try {
      print('🔍 FriendRequestRepositoryImpl.getFriends: fetching friends list');
      final response = await _remoteDataSource.get('/friendship/friends');
      print('🔍 FriendRequestRepositoryImpl.getFriends: response=$response');
      
      final friends = <String>[];
      for (final friendData in response['friends'] ?? []) {
        if (friendData is Map<String, dynamic> && friendData['user_id'] != null) {
          friends.add(friendData['user_id']);
        } else if (friendData is String) {
          friends.add(friendData);
        }
      }
      
      print('🔍 FriendRequestRepositoryImpl.getFriends: parsed ${friends.length} friends');
      return Result.success(friends);
    } catch (e) {
      print('🔍 FriendRequestRepositoryImpl.getFriends: error=$e');
      return Result.failure(const NetworkException(message: 'Failed to get friends list'));
    }
  }
}
