import 'package:flutter/material.dart';
import 'custom_text_field.dart';

/// A dialog for editing user's first name, last name, and username.
/// 
/// This dialog follows the app's design patterns with glassmorphic styling
/// and consistent form validation.
class NameEditDialog extends StatefulWidget {
  const NameEditDialog({
    required this.initialFirstName,
    required this.initialLastName,
    required this.initialUsername,
    super.key,
    this.onSave,
  });

  final String initialFirstName;
  final String initialLastName;
  final String initialUsername;
  final Function(String firstName, String lastName, String username)? onSave;

  /// Shows the name edit dialog.
  static Future<Map<String, String>?> show(
    BuildContext context, {
    required String initialFirstName,
    required String initialLastName,
    required String initialUsername,
  }) => showDialog<Map<String, String>>(
    context: context,
    barrierColor: Colors.black.withValues(alpha: 0.9),
    builder: (context) => NameEditDialog(
      initialFirstName: initialFirstName,
      initialLastName: initialLastName,
      initialUsername: initialUsername,
    ),
  );

  @override
  State<NameEditDialog> createState() => _NameEditDialogState();
}

class _NameEditDialogState extends State<NameEditDialog> {
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _usernameController;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _firstNameController = TextEditingController(text: widget.initialFirstName);
    _lastNameController = TextEditingController(text: widget.initialLastName);
    _usernameController = TextEditingController(text: widget.initialUsername);
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _usernameController.dispose();
    super.dispose();
  }

  String? _validateFirstName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your first name';
    }
    if (value.length < 2) {
      return 'First name must be at least 2 characters';
    }
    if (value.length > 50) {
      return 'First name must be less than 50 characters';
    }
    final nameRegex = RegExp(r"^[\p{L}\p{M}\-']+$", unicode: true);
    if (!nameRegex.hasMatch(value)) {
      return 'Please enter only letters, hyphens, and apostrophes';
    }
    return null;
  }

  String? _validateLastName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your last name';
    }
    if (value.length < 2) {
      return 'Last name must be at least 2 characters';
    }
    if (value.length > 50) {
      return 'Last name must be less than 50 characters';
    }
    final nameRegex = RegExp(r"^[\p{L}\p{M}\-']+$", unicode: true);
    if (!nameRegex.hasMatch(value)) {
      return 'Please enter only letters, hyphens, and apostrophes';
    }
    return null;
  }

  String? _validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a username';
    }
    if (value.length < 3) {
      return 'Username must be at least 3 characters';
    }
    if (value.length > 20) {
      return 'Username must be less than 20 characters';
    }
    final usernameRegex = RegExp(r'^[a-zA-Z0-9_.-]+$');
    if (!usernameRegex.hasMatch(value)) {
      return 'Username can only contain letters, numbers, underscore, dot, and hyphen';
    }
    
    // Check for reserved usernames
    const reservedUsernames = [
      'admin', 'root', 'user', 'test', 'guest', 'null', 'undefined',
      'api', 'www', 'mail', 'support', 'help', 'info', 'contact',
    ];
    if (reservedUsernames.contains(value.toLowerCase())) {
      return 'This username is not available';
    }
    
    return null;
  }

  void _handleSave() {
    if (_formKey.currentState?.validate() ?? false) {
      final result = {
        'firstName': _firstNameController.text.trim(),
        'lastName': _lastNameController.text.trim(),
        'username': _usernameController.text.trim(),
      };
      
      widget.onSave?.call(
        result['firstName']!,
        result['lastName']!,
        result['username']!,
      );
      
      Navigator.of(context).pop(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;

    return AlertDialog(
      backgroundColor: const Color(0xFF1A2B4D),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(40)),
      title: const Text(
        'Edit your profile',
        style: TextStyle(
          color: Color(0xFF00FFFF),
          fontWeight: FontWeight.bold,
          fontFamily: 'Omnes',
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomTextField(
                controller: _firstNameController,
                hintText: 'First name',
                validator: _validateFirstName,
              ),
              SizedBox(height: spacingHeight),
              CustomTextField(
                controller: _lastNameController,
                hintText: 'Last name',
                validator: _validateLastName,
              ),
              SizedBox(height: spacingHeight),
              CustomTextField(
                controller: _usernameController,
                hintText: 'Username',
                validator: _validateUsername,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancel',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Omnes',
            ),
          ),
        ),
        TextButton(
          onPressed: _handleSave,
          child: const Text(
            'Save',
            style: TextStyle(
              color: Color(0xFF00FFFF),
              fontWeight: FontWeight.bold,
              fontFamily: 'Omnes',
            ),
          ),
        ),
      ],
    );
  }
}
