import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../statefulbusinesslogic/bloc/notification/notification_bloc.dart';
import '../../statefulbusinesslogic/bloc/notification/notification_event.dart';
import '../../statefulbusinesslogic/bloc/notification/notification_state.dart';
import '../../statefulbusinesslogic/core/models/notification_model.dart'
    as notification_model;
import '../../statefulbusinesslogic/core/models/notification_model.dart';
import '../pages/chat/chat_page.dart';
import 'requests/contact_request_dialog.dart';

/// A custom styled dialog specifically for displaying notifications.
class NotificationsDialog extends StatefulWidget {
  const NotificationsDialog({super.key});

  /// Static method to easily show the dialog.
  static Future<void> show(BuildContext context) => showDialog<void>(
      context: context,
      barrierColor: Colors.black.withOpacity(
        0.85,
      ), // Set darkening effect to 0.9 opacity
      // barrierDismissible: false, // Optional: prevent dismissing by tapping outside
      builder: (dialogContext) {
        return const NotificationsDialog(); // Return instance of this widget
      },
    );

  @override
  State<NotificationsDialog> createState() => _NotificationsDialogState();
}

class _NotificationsDialogState extends State<NotificationsDialog> {

  // Helper methods for responsive text sizes (similar to profile pages)
  double _getTitleSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    // Example sizes - adjust as needed
    return width < 360 ? 16.0 : (width < 600 ? 18.0 : 20.0);
  }

  double _getBodyTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360 ? 12.0 : (width < 600 ? 14.0 : 16.0);
  }

  double _getButtonTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360 ? 14.0 : (width < 600 ? 16.0 : 18.0);
  }
  // --- End Helper Methods ---

  @override
  void initState() {
    super.initState();
    // Trigger fetch notifications when dialog opens - ONLY ONCE
    context.read<NotificationBloc>().add(const FetchNotifications());
  }

  @override
  Widget build(BuildContext context) {

    // Use the AlertDialog structure with custom styling
    return AlertDialog(
      backgroundColor: Colors.transparent, // Make dialog background transparent
      elevation: 0, // Remove shadow since background is transparent
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      // Adjust horizontal padding for title and content
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      titlePadding: const EdgeInsets.only(
        top: 24,
        left: 24,
        right: 24,
        bottom: 8,
      ),
      contentPadding: const EdgeInsets.symmetric(
        vertical: 12,
      ), // Content (ListView) handles its own item padding
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Notifications', // Dialog Title
            textAlign: TextAlign.left, // Align title left
            style: TextStyle(
              color: Colors.white, // White title
              fontWeight: FontWeight.bold,
              fontSize: _getTitleSize(context), // Responsive title size
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextButton(
                onPressed: () {
                  context.read<NotificationBloc>().add(
                    const MarkAllNotificationsAsRead(),
                  );
                },
                child: Text(
                  'Mark all read',
                  style: TextStyle(
                    color: const Color(0xFF00FFFF),
                    fontSize: _getBodyTextSize(context) * 0.8,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: () {
                  context.read<NotificationBloc>().add(
                    const DeleteAllNotifications(),
                  );
                },
                child: Text(
                  'Clear all',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: _getBodyTextSize(context) * 0.8,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      content: BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
          if (state is NotificationLoading) {
            return const SizedBox(
              height: 100,
              width: 100,
              child: Center(
                child: CircularProgressIndicator(color: Color(0xFF00FFFF)),
              ),
            );
          } else if (state is NotificationsLoaded) {
            return SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.5,
              child:
                  state.notifications.isEmpty
                      ? _buildEmptyNotifications(context)
                      : _buildNotificationsList(context, state.notifications),
            );
          } else if (state is NotificationError) {
            return SizedBox(
              height: 100,
              child: Center(
                child: Text(
                  'Error: ${state.message}',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: _getBodyTextSize(context),
                  ),
                ),
              ),
            );
          } else {
            return const SizedBox(
              height: 100,
              width: 100,
              child: Center(
                child: CircularProgressIndicator(color: Color(0xFF00FFFF)),
              ),
            );
          }
        },
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        TextButton(
          child: Text(
            'Close', // Changed button text to match example image
            style: TextStyle(
              color: const Color(0xFF00FFFF),
              fontSize: _getButtonTextSize(context), // Responsive button size
            ), // Adjusted style
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  Widget _buildEmptyNotifications(BuildContext context) => Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'No new notifications right now.',
          textAlign: TextAlign.center, // Keep content centered
          style: TextStyle(
            color: Colors.white,
            fontSize: _getBodyTextSize(context), // Responsive body size
          ),
        ),
        const SizedBox(height: 10),
        Text(
          'Check back later!',
          textAlign: TextAlign.center, // Keep content centered
          style: TextStyle(
            color: Colors.white70,
            fontSize:
                _getBodyTextSize(context) *
                0.9, // Slightly smaller responsive size
          ),
        ),
      ],
    );

  Widget _buildNotificationsList(
    BuildContext context,
    List<notification_model.Notification> notifications,
  ) {
    // Filter out removed notification types
    final filteredNotifications = notifications
        .where((notification) => !_isRemovedNotificationType(notification.category))
        .toList();

    // If all notifications were filtered out, show empty state
    if (filteredNotifications.isEmpty) {
      return _buildEmptyNotifications(context);
    }

    return ListView.separated(
      itemCount: filteredNotifications.length,
      separatorBuilder:
          (context, index) => const Divider(color: Colors.white24, height: 1),
      itemBuilder: (context, index) {
        final notification = filteredNotifications[index];

        return _buildNotificationTile(context, notification);
      },
    );
  }

  Widget _buildNotificationTile(
    BuildContext context,
    notification_model.Notification notification,
  ) {
    final isRead = notification.isRead;

    // Use a ValueKey based on the unique notification ID
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        key: ValueKey(notification.id),
        contentPadding: const EdgeInsets.only(left: 12, right: 8),
        leading: Container(
          width: 52, // Increased width to accommodate larger profile pictures
          height: 52, // Increased height to accommodate larger profile pictures
          margin: const EdgeInsets.only(), // Reduced space between icon and text
          child: Stack(
            alignment: Alignment.center, // Center the icon within the container
            children: [
              FutureBuilder<Widget>(
                future: _buildNotificationIcon(notification),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    // Show a loading indicator while fetching the profile picture
                    return Container(
                      width: 44, // Consistent with profile picture containers
                      height: 44, // Consistent with profile picture containers
                      alignment: Alignment.center, // Center the avatar
                      child: CircleAvatar(
                        radius: 22, // Consistent with profile pictures
                        backgroundColor: isRead
                            ? Colors.white24
                            : const Color(0xFF00FFFF).withValues(alpha: 0.3),
                        child: const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white70,
                          ),
                        ),
                      ),
                    );
                  } else if (snapshot.hasError || !snapshot.hasData) {
                    // Fallback to the default icon if there's an error
                    final iconData = _getIconForCategory(notification.category);
                    return Container(
                      width: 44, // Consistent with profile picture containers
                      height: 44, // Consistent with profile picture containers
                      alignment: Alignment.center, // Center the avatar
                      child: CircleAvatar(
                        radius: 22, // Consistent with profile pictures
                        backgroundColor: isRead
                            ? Colors.white24
                            : const Color(0xFF00FFFF).withValues(alpha: 0.3),
                        child: Icon(
                          iconData,
                          color: isRead ? Colors.white70 : const Color(0xFF00FFFF),
                          size: 20,
                        ),
                      ),
                    );
                  } else {
                    // Show the custom icon or profile picture
                    return snapshot.data!;
                  }
                },
              ),
                Positioned(
                  left: 36,
                  bottom: 36,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Color(0xFF00FFFF),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            
            ],
          ),
        ),
        title: Text(
          _personalizeNotificationMessage(notification),
          style: TextStyle(
            color: Colors.white,
            fontSize: _getBodyTextSize(context) * 0.9,
            fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Text(
          _formatTimestamp(notification.createdAt),
          style: TextStyle(
            color: Colors.white54,
            fontSize: _getBodyTextSize(context) * 0.7,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min, // Important to keep row compact
          children: [
            // Mark as Unread Button (only visible if read)
            if (isRead)
              IconButton(
                icon: const Icon(Icons.mark_email_unread_outlined, size: 20),
                color: Colors.white54,
                tooltip: 'Mark as unread',
                onPressed: () {
                  context.read<NotificationBloc>().add(
                    MarkNotificationAsUnread(notification.id),
                  );
                },
              ),
            // Delete Button
            IconButton(
              icon: const Icon(Icons.delete_outline, size: 20),
              color: Colors.white54,
              tooltip: 'Delete notification',
              onPressed: () {
                context.read<NotificationBloc>().add(
                  DeleteNotification(notification.id),
                );
              },
            ),
          ],
        ),
        onTap: () {
          print(
            '[UI Tap] onTap invoked for tile visually holding notification ID: ${notification.id}',
          );

          // Skip handling for removed notification types
          if (_isRemovedNotificationType(notification.category)) {
            // Delete the notification since it's a removed type
            context.read<NotificationBloc>().add(
              DeleteNotification(notification.id),
            );
            return;
          }

          // Mark notification as read
          if (!isRead) {
            context.read<NotificationBloc>().add(
              MarkNotificationAsRead(notification.id),
            );
          }

          print(
            'Tapped notification: ${notification.id}, Payload: ${notification.payload}',
          );

          // Handle different notification types
          if (notification.category == NotificationCategory.contactRequest) {
            _handleContactRequestNotification(context, notification);
          }
        },
      ),
    );
  }

  IconData _getIconForCategory(
    notification_model.NotificationCategory category,
  ) {
    switch (category) {
      // Contact/Friend Requests
      case notification_model.NotificationCategory.contactRequestReceived:
        return Icons.person_add_alt_1;
      case notification_model.NotificationCategory.contactRequestAccepted:
        return Icons.how_to_reg;
      case notification_model.NotificationCategory.contactRequestDeclined:
        return Icons.person_remove;
      case notification_model.NotificationCategory.friendshipEstablished:
        return Icons.people_alt;

      // Bubble Invitations & Join Requests
      case notification_model.NotificationCategory.bubbleInvitationReceived:
        return Icons.card_membership;
      case notification_model.NotificationCategory.bubbleJoinRequestReceived:
        return Icons.group_add;
      case notification_model.NotificationCategory.bubbleJoinRequestAccepted:
        return Icons.group_work;
      case notification_model.NotificationCategory.bubbleJoinRequestRejected:
        return Icons.block;
      case notification_model.NotificationCategory.bubbleMemberJoined:
        return Icons.groups;

      // Bubble Management & Interaction
      case notification_model.NotificationCategory.bubbleVotekickInitiated:
        return Icons.gavel;
      case notification_model.NotificationCategory.bubbleVotekickPassed:
        return Icons.person_off;

      // Bubble Messages & Calls
      case notification_model.NotificationCategory.bubbleChatMessageReceived:
        return Icons.chat_bubble;
      case notification_model.NotificationCategory.bubbleVoiceMessageReceived:
        return Icons.mic_none;
      case notification_model.NotificationCategory.bubbleVideoMessageReceived:
        return Icons.videocam;
      case notification_model.NotificationCategory.bubbleAudioCallIncoming:
        return Icons.add_ic_call;
      case notification_model.NotificationCategory.bubbleVideoCallIncoming:
        return Icons.video_call;
      case notification_model.NotificationCategory.bubbleScreenShareIncoming:
        return Icons.screen_share;
      case notification_model.NotificationCategory.bubbleCallInProgress:
        return Icons.phone_in_talk;
      case notification_model.NotificationCategory.bubbleCallEnded:
        return Icons.call_end;
      case notification_model.NotificationCategory.bubbleMissedCall:
        return Icons.phone_missed;

      // Bubble Lifecycle
      case notification_model.NotificationCategory.bubblePopReminder60Days:
      case notification_model.NotificationCategory.bubblePopReminder30Days:
      case notification_model.NotificationCategory.bubblePopReminder20Days:
      case notification_model.NotificationCategory.bubblePopReminder10Days:
      case notification_model.NotificationCategory.bubblePopReminder7Days:
      case notification_model.NotificationCategory.bubblePopReminder3Days:
      case notification_model.NotificationCategory.bubblePopReminder24Hours:
        return Icons.hourglass_empty;

      // Direct Friend Interactions
      case notification_model.NotificationCategory.friendChatMessageReceived:
        return Icons.message;
      case notification_model.NotificationCategory.friendVoiceMessageReceived:
        return Icons.spatial_audio_off;
      case notification_model.NotificationCategory.friendVideoMessageReceived:
        return Icons.personal_video;
      case notification_model.NotificationCategory.friendAudioCallIncoming:
        return Icons.call;
      case notification_model.NotificationCategory.friendVideoCallIncoming:
        return Icons.videocam;
      case notification_model.NotificationCategory.friendScreenShareIncoming:
        return Icons.screen_share;
      case notification_model.NotificationCategory.friendMissedCall:
        return Icons.call_missed_outgoing;
      case notification_model.NotificationCategory.friendCallInProgress:
        return Icons.phone_in_talk;

      // User Activity & Engagement
      case notification_model.NotificationCategory.inactiveNoBubble1Day:
      case notification_model.NotificationCategory.inactiveNoBubble2Days:
      case notification_model.NotificationCategory.inactiveNoBubble3Days:
      case notification_model.NotificationCategory.inactiveNoBubble7Days:
        return Icons.self_improvement;

      // General Categories
      case notification_model.NotificationCategory.statusUpdates:
        return Icons.update;
      case notification_model.NotificationCategory.securityAlerts:
        return Icons.security;
      case notification_model.NotificationCategory.appUpdates:
        return Icons.system_update;

      // Legacy categories (deprecated)
      case notification_model.NotificationCategory.contactRequest:
        return Icons.person_add_alt_1;
      case notification_model.NotificationCategory.bubbleInvite:
        return Icons.card_membership;
      case notification_model.NotificationCategory.message:
        return Icons.chat_bubble;
      case notification_model.NotificationCategory.call:
        return Icons.add_ic_call;
      case notification_model.NotificationCategory.reminder:
        return Icons.hourglass_empty;
      case notification_model.NotificationCategory.system:
        return Icons.notifications;

      default:
        return Icons.notifications;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }

  /// Handles a contact request notification.
  ///
  /// Shows the ContactRequestDialog when the notification is tapped.
  void _handleContactRequestNotification(
    BuildContext context,
    notification_model.Notification notification,
  ) {
    // Extract contact request information from the notification payload
    final payload = notification.payload;
    if (payload == null) {
      print('Error: Contact request notification payload is null');
      return;
    }

    final requesterId = payload['requester_id'] as String? ?? payload['senderId'] as String?;
    final requesterName = payload['requester_name'] as String? ?? payload['senderName'] as String?;
    final requesterUsername = payload['requester_username'] as String? ?? payload['senderUsername'] as String?;
    final requesterProfilePicUrl = payload['requester_profile_pic_url'] as String? ?? payload['senderProfilePicUrl'] as String?;

    if (requesterId == null || requesterName == null) {
      print('Error: Missing requester information in notification payload');
      return;
    }

    // Close the notifications dialog
    Navigator.of(context).pop();

    // Show the contact request dialog
    ContactRequestDialog.show(
      context,
      requestId: notification.id, // Use notification ID as request ID
      requesterId: requesterId,
      requesterName: requesterName,
      requesterUsername: requesterUsername,
      requesterProfilePicUrl: requesterProfilePicUrl,
      requestTimestamp: notification.createdAt,
    );
  }



  /// Builds the appropriate icon for a notification based on its category.
  ///
  /// For user-related notifications, it displays user information from the notification payload.
  /// For other notifications, it displays a custom image or falls back to a default icon.
  Future<Widget> _buildNotificationIcon(
    notification_model.Notification notification,
  ) async {
    final isRead = notification.isRead;
    final backgroundColor = isRead
        ? Colors.white24
        : const Color(0xFF00FFFF).withValues(alpha: 0.3);
    final iconColor = isRead ? Colors.white70 : const Color(0xFF00FFFF);

    // Check if this notification type should display a user profile picture
    if (_shouldShowUserProfilePicture(notification.category)) {
      // Extract user information from the notification payload
      final payload = notification.payload;
      final profilePictureUrl = payload?['avatarUrl'] as String?;
      final userName = payload?['userName'] as String? ?? payload?['name'] as String? ?? 'User';

      if (profilePictureUrl != null && profilePictureUrl.isNotEmpty) {
        // Display the user's profile picture
        return Container(
          width: 44, // Slightly larger container for bigger profile pictures
          height: 44, // Slightly larger container for bigger profile pictures
          alignment: Alignment.center, // Center the avatar
          child: CircleAvatar(
            radius: 22, // Increased profile picture size
            backgroundColor: backgroundColor,
            backgroundImage: NetworkImage(profilePictureUrl),
            onBackgroundImageError: (_, __) {
              // If the image fails to load, we'll show the fallback
              print('Failed to load profile picture from notification payload');
            },
          ),
        );
      } else {
        // Fallback to showing the first letter of the user's name
        return Container(
          width: 44, // Slightly larger container for bigger profile pictures
          height: 44, // Slightly larger container for bigger profile pictures
          alignment: Alignment.center, // Center the avatar
          child: CircleAvatar(
            radius: 22, // Increased profile picture size
            backgroundColor: backgroundColor,
            child: Text(
              userName.isNotEmpty ? userName[0].toUpperCase() : '?',
              style: TextStyle(
                color: iconColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      }
    }

    // Check if this notification type should display a custom image
    final imagePath = _getCustomImagePath(notification.category);
    if (imagePath != null) {
      return Container(
        width: 44, // Consistent with profile picture containers
        height: 44, // Consistent with profile picture containers
        alignment: Alignment.center, // Center the image in the container
        child: Image.asset(
          imagePath,
          width: 40, // Increased size to match profile pictures
          height: 40, // Increased size to match profile pictures
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            print('Error loading image $imagePath: $error');
            // Fallback to icon if image fails to load
            return CircleAvatar(
              backgroundColor: backgroundColor,
              child: Icon(
                _getIconForCategory(notification.category),
                color: iconColor,
                size: 20,
              ),
            );
          },
        ),
      );
    }

    // Default to using an icon
    return Container(
      width: 44, // Consistent with profile picture containers
      height: 44, // Consistent with profile picture containers
      alignment: Alignment.center, // Center the avatar
      child: CircleAvatar(
        radius: 22, // Consistent with profile pictures
        backgroundColor: backgroundColor,
        child: Icon(
          _getIconForCategory(notification.category),
          color: iconColor,
          size: 20,
        ),
      ),
    );
  }

  /// Determines if a notification category should display a user profile picture.
  bool _shouldShowUserProfilePicture(notification_model.NotificationCategory category) {
    switch (category) {
      // Contact/Friend Requests - show profile pictures
      case notification_model.NotificationCategory.contactRequestReceived:
      case notification_model.NotificationCategory.contactRequestAccepted:
      case notification_model.NotificationCategory.contactRequestDeclined:
      case notification_model.NotificationCategory.friendshipEstablished:

      // Bubble Interactions - show profile pictures
      case notification_model.NotificationCategory.bubbleInvitationReceived:
      case notification_model.NotificationCategory.bubbleJoinRequestReceived:
      case notification_model.NotificationCategory.bubbleMemberJoined:

      // Friend Communications - show profile pictures
      case notification_model.NotificationCategory.friendChatMessageReceived:
      case notification_model.NotificationCategory.friendVoiceMessageReceived:
      case notification_model.NotificationCategory.friendVideoMessageReceived:
      case notification_model.NotificationCategory.friendAudioCallIncoming:
      case notification_model.NotificationCategory.friendVideoCallIncoming:
      case notification_model.NotificationCategory.friendScreenShareIncoming:
      case notification_model.NotificationCategory.friendMissedCall:
      case notification_model.NotificationCategory.friendCallInProgress:

      // Legacy categories
      case notification_model.NotificationCategory.contactRequest:
      case notification_model.NotificationCategory.bubbleInvite:
      case notification_model.NotificationCategory.message:
      case notification_model.NotificationCategory.call:
        return true;

      // All other categories use custom images or fallback icons
      default:
        return false;
    }
  }

  /// Extracts the relevant user ID from a notification payload.
  String? _getUserIdFromPayload(notification_model.Notification notification) {
    final payload = notification.payload;
    if (payload == null) return null;

    switch (notification.category) {
      case notification_model.NotificationCategory.contactRequest:
        return payload['senderId'] as String?;
      case notification_model.NotificationCategory.bubbleInvite:
        return payload['inviterId'] as String?;
      case notification_model.NotificationCategory.message:
        return payload['senderId'] as String?;
      case notification_model.NotificationCategory.call:
        return payload['callerId'] as String?;
      case notification_model.NotificationCategory.reminder:
        return payload['bubbleId'] as String?;
      case notification_model.NotificationCategory.system:
        return payload['userId'] as String?;
      default:
        return null;
    }
  }

  /// Returns the custom image path for a notification category, or null if no custom image is defined.
  String? _getCustomImagePath(notification_model.NotificationCategory category) {
    switch (category) {
      // Bubble Join Requests
      case notification_model.NotificationCategory.bubbleJoinRequestAccepted:
        return 'assets/images/3d/200px/normal/chat.png';
      case notification_model.NotificationCategory.bubbleJoinRequestRejected:
        return 'assets/images/3d/200px/normal/thumb-down.png';

      // Bubble Management
      case notification_model.NotificationCategory.bubbleVotekickInitiated:
      case notification_model.NotificationCategory.bubbleVotekickPassed:
        return 'assets/images/3d/200px/normal/flash.png';

      // Bubble Communications
      case notification_model.NotificationCategory.bubbleChatMessageReceived:
        return 'assets/images/3d/200px/normal/chat.png';
      case notification_model.NotificationCategory.bubbleVoiceMessageReceived:
        return 'assets/images/3d/200px/normal/mic.png';
      case notification_model.NotificationCategory.bubbleVideoMessageReceived:
        return 'assets/images/3d/200px/normal/video-cam.png';
      case notification_model.NotificationCategory.bubbleAudioCallIncoming:
        return 'assets/images/3d/200px/normal/call-in.png';
      case notification_model.NotificationCategory.bubbleVideoCallIncoming:
        return 'assets/images/3d/200px/normal/video-cam.png';
      case notification_model.NotificationCategory.bubbleScreenShareIncoming:
        return 'assets/images/3d/200px/normal/mobile.png';
      case notification_model.NotificationCategory.bubbleCallInProgress:
        return 'assets/images/3d/200px/normal/call-ringing.png';
      case notification_model.NotificationCategory.bubbleCallEnded:
        return 'assets/images/3d/200px/normal/call-end.png';
      case notification_model.NotificationCategory.bubbleMissedCall:
        return 'assets/images/3d/200px/normal/chat.png';

      // Bubble Lifecycle
      case notification_model.NotificationCategory.bubblePopReminder60Days:
      case notification_model.NotificationCategory.bubblePopReminder30Days:
      case notification_model.NotificationCategory.bubblePopReminder20Days:
      case notification_model.NotificationCategory.bubblePopReminder10Days:
      case notification_model.NotificationCategory.bubblePopReminder7Days:
      case notification_model.NotificationCategory.bubblePopReminder3Days:
      case notification_model.NotificationCategory.bubblePopReminder24Hours:
        return 'assets/images/3d/200px/normal/calender.png';

      // User Engagement
      case notification_model.NotificationCategory.inactiveNoBubble1Day:
      case notification_model.NotificationCategory.inactiveNoBubble2Days:
      case notification_model.NotificationCategory.inactiveNoBubble3Days:
      case notification_model.NotificationCategory.inactiveNoBubble7Days:
        return 'assets/images/3d/200px/normal/notify-heart.png';

      // General Categories
      case notification_model.NotificationCategory.statusUpdates:
        return 'assets/images/3d/200px/normal/tick.png';
      case notification_model.NotificationCategory.securityAlerts:
        return 'assets/images/3d/200px/normal/shield.png';
      case notification_model.NotificationCategory.appUpdates:
        return 'assets/images/3d/200px/normal/rocket.png';

      // Legacy categories
      case notification_model.NotificationCategory.bubbleInvite:
        return 'assets/images/3d/200px/normal/chat.png';
      case notification_model.NotificationCategory.message:
        return 'assets/images/3d/200px/normal/chat.png';
      case notification_model.NotificationCategory.call:
        return 'assets/images/3d/200px/normal/call-in.png';
      case notification_model.NotificationCategory.reminder:
        return 'assets/images/3d/200px/normal/calender.png';
      case notification_model.NotificationCategory.system:
        return 'assets/images/3d/200px/normal/tick.png';

      // Categories that use profile pictures or fallback icons only
      default:
        return null;
    }
  }

  /// Checks if a notification type is one of the removed types that should not be displayed.
  bool _isRemovedNotificationType(notification_model.NotificationCategory category) {
    // For now, no notification types are considered "removed" since we only have basic categories
    return false;
  }

  /// Personalizes bubble-related notification messages by replacing specific bubble names
  /// with "your bubble" for bubbles the user is a member of.
  ///
  /// For notifications about bubbles the user is not a member of (like invitations),
  /// it preserves the actual bubble name.
  String _personalizeNotificationMessage(notification_model.Notification notification) {
    final originalMessage = notification.message;
    final payload = notification.payload;

    // If there's no payload or it doesn't contain bubble information, return the original message
    if (payload == null || !payload.containsKey('bubbleId')) {
      return originalMessage;
    }

    // Check if this is a notification about a bubble the user is a member of
    var isUserMember = true;

    // For bubble invitation notifications, the user is not yet a member
    if (notification.category == notification_model.NotificationCategory.bubbleInvite) {
      isUserMember = false;
    }

    // If the user is not a member, keep the original bubble name
    if (!isUserMember) {
      return originalMessage;
    }

    // Replace the bubble name with "your bubble" for notifications about bubbles the user is a member of
    final bubbleName = payload['bubbleName'] as String? ?? '';
    if (bubbleName.isNotEmpty) {
      // Use regex to replace the bubble name (which might be in quotes) with "your bubble"
      final pattern = RegExp('"$bubbleName"|\'$bubbleName\'|$bubbleName');
      return originalMessage.replaceAll(pattern, 'your bubble');
    }

    return originalMessage;
  }
}
