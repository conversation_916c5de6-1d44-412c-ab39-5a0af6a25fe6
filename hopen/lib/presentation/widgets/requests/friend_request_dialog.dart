import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../utils/color_transition_controller.dart';
import '../../../statefulbusinesslogic/bloc/friend_request/friend_request_bloc.dart';
import '../../../statefulbusinesslogic/bloc/friend_request/friend_request_event.dart';
import '../../../statefulbusinesslogic/bloc/friend_request/friend_request_state.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';
import '../animated_gradient_text.dart';

/// A dialog that appears when another user sends an auto-generated friend request
/// after a bubble expires.
///
/// Features:
/// * Displays the requester's profile picture and name
/// * Shows the bubble name that triggered the friend request
/// * Shows the timestamp of when the request was made
/// * Provides accept/decline buttons with appropriate confirmation messages
/// * Uses the same design pattern and UI style as the existing dialogs
/// * Includes animations and transitions consistent with other dialogs
class FriendRequestDialog extends StatefulWidget {
  const FriendRequestDialog({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requestTimestamp,
    required this.sourceBubbleId,
    super.key,
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.bubbleName,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final DateTime requestTimestamp;
  final String sourceBubbleId;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final String? bubbleName;

  /// Shows the friend request dialog.
  static Future<bool?> show(
    BuildContext context, {
    required String requestId,
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    required String sourceBubbleId,
    String? requesterUsername,
    String? requesterProfilePicUrl,
    String? bubbleName,
  }) => showDialog<bool>(
    context: context,
    barrierDismissible: false, // Make the dialog non-dismissible
    barrierColor: Colors.black.withValues(alpha: 0.9),
    builder:
        (context) => WillPopScope(
          // Prevent back button from dismissing the dialog
          onWillPop: () async => false,
          child: FriendRequestDialog(
            requestId: requestId,
            requesterId: requesterId,
            requesterName: requesterName,
            requesterUsername: requesterUsername,
            requesterProfilePicUrl: requesterProfilePicUrl,
            requestTimestamp: requestTimestamp,
            sourceBubbleId: sourceBubbleId,
            bubbleName: bubbleName,
          ),
        ),
  );

  @override
  State<FriendRequestDialog> createState() => _FriendRequestDialogState();
}

class _FriendRequestDialogState extends State<FriendRequestDialog> {
  late FriendRequestBloc _friendRequestBloc;
  bool _isSubmitting = false;
  String? _errorMessage;

  /// Controller for managing color transitions
  late ColorTransitionController _colorController;

  @override
  void initState() {
    super.initState();
    _friendRequestBloc = BlocProvider.of<FriendRequestBloc>(context);
    _colorController = ColorTransitionController();
    _colorController.startColorLoop();

    // Listen to color changes and update the UI
    _colorController.addListener(() {
      if (mounted) setState(() {});
    });

    // Load the friend request data
    _friendRequestBloc.add(LoadFriendRequestEvent(
      requestId: widget.requestId,
      requesterId: widget.requesterId,
      requesterName: widget.requesterName,
      sourceBubbleId: widget.sourceBubbleId,
      requesterUsername: widget.requesterUsername,
      requesterProfilePicUrl: widget.requesterProfilePicUrl,
      bubbleName: widget.bubbleName,
    ));
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 400;
    final dialogWidth = isSmallScreen ? screenSize.width * 0.9 : 350.0;
    final imageSize = isSmallScreen ? 80.0 : 100.0;
    final verticalSpacing = isSmallScreen ? 3.0 : 4.0;

    return BlocListener<FriendRequestBloc, FriendRequestState>(
      listener: (context, state) {
        if (state.status == FriendRequestStatus.accepted) {
          _showSuccessMessage(context);
        } else if (state.status == FriendRequestStatus.declined) {
          Navigator.of(context).pop(false);
        } else if (state.status == FriendRequestStatus.error) {
          setState(() {
            _errorMessage = state.errorMessage ?? 'An error occurred';
          });
        }
      },
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: dialogWidth,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1a1a2e),
                Color(0xFF16213e),
                Color(0xFF0f3460),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: _buildDialogContent(context, imageSize, verticalSpacing),
        ),
      ),
    );
  }

  Widget _buildDialogContent(BuildContext context, double imageSize, double verticalSpacing) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: verticalSpacing * 2),
          AnimatedGradientText(
            text: 'Friend request',
            style: TextStyle(
              fontSize: ColorTransitionController.getTitleSize(context),
              fontWeight: FontWeight.bold,
              fontFamily: 'Omnes',
            ),
            colors: const [
              Color(0xFF00A9FF),
              Color(0xFF0078D4),
              Color(0xFF00A9FF),
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
          SizedBox(height: verticalSpacing * 8),
          _buildRequesterProfile(context, imageSize * 1.4),
          SizedBox(height: verticalSpacing * 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              widget.bubbleName != null
                ? '${widget.requesterName} wants to be friends after "${widget.bubbleName}" ended'
                : '${widget.requesterName} wants to be friends',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: ColorTransitionController.getSubtitleSize(context),
                height: 1.4,
              ),
            ),
          ),
          SizedBox(height: verticalSpacing * 0.8),
          Text(
            'Request sent ${_formatTimestamp(widget.requestTimestamp)}',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white54,
              fontSize: ColorTransitionController.getSubtitleSize(context) * 0.8,
            ),
          ),
          if (_errorMessage != null) ...[
            SizedBox(height: verticalSpacing * 2),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.redAccent,
                fontSize: 14,
              ),
            ),
          ],
          SizedBox(height: verticalSpacing * 8),
          _buildActionButtons(context),
          SizedBox(height: verticalSpacing * 2),
        ],
      ),
    );
  }

  Widget _buildRequesterProfile(BuildContext context, double imageSize) {
    return Column(
      children: [
        Container(
          width: imageSize,
          height: imageSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: ClipOval(
            child: widget.requesterProfilePicUrl != null
                ? Image.network(
                    widget.requesterProfilePicUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(imageSize),
                  )
                : _buildDefaultAvatar(imageSize),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          widget.requesterName,
          style: TextStyle(
            color: Colors.white,
            fontSize: ColorTransitionController.getSubtitleSize(context) * 1.1,
            fontWeight: FontWeight.w600,
            fontFamily: 'Omnes',
          ),
        ),
        if (widget.requesterUsername != null) ...[
          const SizedBox(height: 4),
          Text(
            '@${widget.requesterUsername}',
            style: TextStyle(
              color: Colors.white70,
              fontSize: ColorTransitionController.getSubtitleSize(context) * 0.9,
              fontFamily: 'Omnes',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDefaultAvatar(double size) {
    return Container(
      width: size,
      height: size,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF00A9FF), Color(0xFF0078D4)],
        ),
      ),
      child: Icon(
        Icons.person,
        size: size * 0.6,
        color: Colors.white,
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return BlocBuilder<FriendRequestBloc, FriendRequestState>(
      builder: (context, state) {
        final isLoading = state.status == FriendRequestStatus.accepting ||
                         state.status == FriendRequestStatus.declining;

        return Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: isLoading ? null : () => _handleDecline(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Colors.white54, width: 1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: isLoading && state.status == FriendRequestStatus.declining
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Decline',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Omnes',
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: isLoading ? null : () => _handleAccept(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF00A9FF),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  elevation: 3,
                ),
                child: isLoading && state.status == FriendRequestStatus.accepting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Accept',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Omnes',
                        ),
                      ),
              ),
            ),
          ],
        );
      },
    );
  }
  void _handleAccept(BuildContext context) {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    _friendRequestBloc.add(AcceptFriendRequestEvent(requestId: widget.requestId));

    // Notify UnifiedProfileBloc about the new friendship
    context.read<UnifiedProfileBloc>().add(
      FriendRequestAcceptedEvent(targetUserId: widget.requesterId),
    );
  }

  void _handleDecline(BuildContext context) {
    if (_isSubmitting) return;

    _showDeclineConfirmation(context);
  }

  void _showDeclineConfirmation(BuildContext context) {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1a1a2e),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          'Decline Friend Request',
          style: TextStyle(color: Colors.white, fontFamily: 'Omnes'),
        ),
        content: Text(
          'Are you sure you want to decline ${widget.requesterName}\'s friend request?',
          style: const TextStyle(color: Colors.white70, fontFamily: 'Omnes'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white54, fontFamily: 'Omnes'),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(true);
              setState(() {
                _isSubmitting = true;
                _errorMessage = null;
              });
              _friendRequestBloc.add(DeclineFriendRequestEvent(requestId: widget.requestId));
            },
            child: const Text(
              'Decline',
              style: TextStyle(color: Colors.redAccent, fontFamily: 'Omnes'),
            ),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(BuildContext context) {
    // Show success message briefly before closing
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: const Color(0xFF1a1a2e),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 48,
              ),
              const SizedBox(height: 16),
              const Text(
                'New friend added!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Omnes',
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You and ${widget.requesterName} are now friends',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontFamily: 'Omnes',
                ),
              ),
            ],
          ),
        ),
      ),
    );

    // Auto-close after 2 seconds
    Timer(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop(); // Close success dialog
        Navigator.of(context).pop(true); // Close main dialog
      }
    });
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }
}