import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import '../../../statefulbusinesslogic/core/models/bubble_member.dart';
import '../gradient_background.dart';
import '../../utils/color_transition_controller.dart';
import '../../../statefulbusinesslogic/bloc/bubble_request/bubble_request_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble_request/bubble_request_event.dart';
import '../../../statefulbusinesslogic/bloc/bubble_request/bubble_request_state.dart';
import '../../../statefulbusinesslogic/bloc/bubble_propose_request/bubble_propose_request_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble_propose_request/bubble_propose_request_event.dart';
import '../../../statefulbusinesslogic/bloc/bubble_propose_request/bubble_propose_request_state.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';
import '../animated_gradient_text.dart';

/// A dialog that appears when a bubble member proposes a new member to join the bubble.
/// All existing members must approve for the new member to join.
///
/// Features:
/// * Displays the proposed member's profile picture and name
/// * Shows the bubble name
/// * Shows the timestamp of when the proposal was made
/// * Provides accept/decline buttons with appropriate confirmation messages
/// * Uses the same design pattern and UI style as the existing dialogs
/// * Includes animations and transitions consistent with other dialogs
class BubbleProposeRequestDialog extends StatefulWidget {
  const BubbleProposeRequestDialog({
    super.key,
    required this.bubbleName,
    required this.members,
    required this.bubbleId,
    required this.proposedMemberId,
  });

  final String bubbleName;
  final List<BubbleMember> members;
  final String bubbleId;
  final String proposedMemberId;

  /// Shows the bubble propose request dialog.
  static Future<bool?> show(
    BuildContext context, {
    required String bubbleName,
    required List<BubbleMember> members,
    required String bubbleId,
    required String proposedMemberId,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // Make the dialog non-dismissible
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder: (context) => BubbleProposeRequestDialog(
        bubbleName: bubbleName,
        members: members,
        bubbleId: bubbleId,
        proposedMemberId: proposedMemberId,
      ),
    );
  }

  @override
  State<BubbleProposeRequestDialog> createState() =>
      _BubbleProposeRequestDialogState();
}

class _BubbleProposeRequestDialogState
    extends State<BubbleProposeRequestDialog> {
  late BubbleProposeRequestBloc _bubbleProposeRequestBloc;
  bool _isSubmitting = false;
  String? _errorMessage;

  /// Controller for managing color transitions
  late ColorTransitionController _colorController;

  @override
  void initState() {
    super.initState();
    _bubbleProposeRequestBloc = BlocProvider.of<BubbleProposeRequestBloc>(context);
    _colorController = ColorTransitionController();
    _colorController.startColorLoop();

    // Listen to color changes and update the UI
    _colorController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  double _getImageSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 120;
    } else if (width < 600) {
      return 140;
    } else {
      return 150;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }

  @override
  Widget build(BuildContext context) =>
      BlocListener<BubbleRequestBloc, BubbleRequestState>(
        listener: (context, state) {
          // Handle state changes
          if (state.status == BubbleRequestStatus.accepting) {
            setState(() {
              _isSubmitting = true;
              _errorMessage = null;
            });
          } else if (state.status == BubbleRequestStatus.accepted) {
            setState(() {
              _isSubmitting = false;
              _errorMessage = null;
            });
            // Show success message
            _showSuccessMessage(context);
          } else if (state.status == BubbleRequestStatus.declining) {
            setState(() {
              _isSubmitting = true;
              _errorMessage = null;
            });
          } else if (state.status == BubbleRequestStatus.declined) {
            setState(() {
              _isSubmitting = false;
              _errorMessage = null;
            });
            // Close the dialog
            Navigator.of(context).pop(false);
          } else if (state.status == BubbleRequestStatus.error) {
            setState(() {
              _isSubmitting = false;
              _errorMessage = state.errorMessage ?? 'An error occurred';
            });
          }
        },
        child: _buildDialogContent(context),
      );

  Widget _buildDialogContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // Get current color and glow effect from the controller
    final currentActiveColor = _colorController.currentColor;
    final titleGlowEffect = _colorController.getGlowEffect();

    final imageSize = _getImageSize(context);
    final verticalSpacing = ColorTransitionController.getDialogVerticalSpacing(
      context,
    );

    return AlertDialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      insetPadding: EdgeInsets.zero,
      titlePadding: const EdgeInsets.only(top: 16),
      contentPadding: const EdgeInsets.symmetric(),
      title: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedGradientText(
              text: 'New bubbler proposal',
              style: TextStyle(
                fontFamily: 'Omnes',
                fontSize: ColorTransitionController.getTitleSize(context),
                fontWeight: FontWeight.bold,
              ),
              colors: const <Color>[
                Color(0xFFFF00FF), // #f0f
                Color(0xFFFB43BB), // #fb43bb
                Color(0xFFF3C935), // #f3c935
                Color(0xFFF0FF00), // #f0ff00
                Color(0xFFC4FF2D), // #c4ff2d
                Color(0xFF91FF64), // #91ff64
                Color(0xFF64FF93), // #64ff93
                Color(0xFF40FFBA), // #40ffba
                Color(0xFF24FFD8), // #24ffd8
                Color(0xFF10FFED), // #10ffed
                Color(0xFF04FFFA), // #04fffa
                Color(0xFF00FFFF), // aqua
              ],
              stops: const <double>[
                0,
                0.12,
                0.37,
                0.48,
                0.53,
                0.6,
                0.67,
                0.74,
                0.81,
                0.88,
                0.94,
                1,
              ],
              duration: const Duration(seconds: 8),
            ),
            SizedBox(height: verticalSpacing * 8),
            _buildProposedMemberProfile(context, imageSize * 1.4),
            SizedBox(height: verticalSpacing * 4),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                '${widget.members[0].name} wants to add ${widget.members[0].name} to "${widget.bubbleName}"',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ColorTransitionController.getSubtitleSize(context),
                  height: 1.4,
                ),
              ),
            ),
            SizedBox(height: verticalSpacing * 0.8),
            Text(
              'Proposal sent ${_formatTimestamp(widget.members[0].proposeTimestamp ?? DateTime.now())}',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white54,
                fontSize:
                    ColorTransitionController.getSubtitleSize(context) * 0.8,
              ),
            ),
          ],
        ),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: screenHeight * 0.1,
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red, fontSize: 14),
                ),
              ),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: Row(
                children: [
                  Expanded(child: _buildDeclineButton(context)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildAcceptButton(context)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProposedMemberProfile(BuildContext context, double imageSize) =>
      Container(
        width: imageSize,
        height: imageSize,
        decoration: ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(imageSize * 0.4),
            side: BorderSide(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          color: Colors.white.withValues(alpha: 0.1),
        ),
        child:
            widget.members[0].profilePicUrl != null &&
                    widget.members[0].profilePicUrl!.isNotEmpty
                ? ClipRRect(
                  borderRadius: BorderRadius.circular(imageSize * 0.4),
                  child: Image.network(
                    widget.members[0].profilePicUrl!,
                    fit: BoxFit.cover,
                    errorBuilder:
                        (context, error, stackTrace) =>
                            _buildFallbackAvatar(imageSize),
                  ),
                )
                : _buildFallbackAvatar(imageSize),
      );

  Widget _buildFallbackAvatar(double imageSize) => Center(
    child: Text(
      widget.members[0].name.isNotEmpty
          ? widget.members[0].name[0].toUpperCase()
          : '?',
      style: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: imageSize * 0.4,
      ),
    ),
  );

  Widget _buildDeclineButton(BuildContext context) => ElevatedButton(
    onPressed: _isSubmitting ? null : () => _handleDecline(context),
    style: ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF4A4A4A), // Darker grey for decline
      foregroundColor: Colors.white,
      disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
      padding: EdgeInsets.zero,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    ).copyWith(elevation: WidgetStateProperty.all(0)),
    child: const Center(
      child: Text(
        'Decline',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    ),
  );

  Widget _buildAcceptButton(BuildContext context) => ElevatedButton(
    onPressed: _isSubmitting ? null : () => _handleAccept(context),
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.blue, // Original blue color
      foregroundColor: Colors.white,
      disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
      padding: EdgeInsets.zero,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    ).copyWith(elevation: WidgetStateProperty.all(0)),
    child: const Center(
      child: Text(
        'Accept',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    ),
  );

  void _handleAccept(BuildContext context) {
    if (_isSubmitting) return;

    _bubbleProposeRequestBloc.add(
      AcceptBubbleProposeRequestEvent(
        bubbleId: widget.bubbleId,
        proposedMemberId: widget.proposedMemberId,
      ),
    );
    // Notify UnifiedProfileBloc
    context.read<UnifiedProfileBloc>().add(
      BubbleRequestAcceptedEvent(targetUserId: widget.proposedMemberId),
    );
  }

  void _showSuccessMessage(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder: (dialogContext) {
        // Auto-close after 2 seconds
        Future.delayed(const Duration(seconds: 2), () {
          if (Navigator.of(dialogContext).canPop()) {
            Navigator.of(dialogContext).pop(); // Close success message dialog
            // Now pop the original BubbleProposeRequestDialog
            if (Navigator.of(context).canPop()) {
              Navigator.of(
                context,
              ).pop(true); // Return true to indicate acceptance
            }
          }
        });

        return WillPopScope(
          // Prevent back button from dismissing the dialog
          onWillPop: () async => false,
          child: AlertDialog(
            backgroundColor: const Color(0xFF1A2B4D),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            contentPadding: const EdgeInsets.all(24),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/3d/200px/normal/tick.png',
                  width: 100,
                  height: 100,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    print('Error loading tick.png: $error');
                    // Fallback to the original icon if image fails to load
                    return const Icon(
                      Icons.check_circle,
                      color: Color(0xFF00FFFF),
                      size: 70,
                    );
                  },
                ),
                const SizedBox(height: 24),
                const Text(
                  'Proposal accepted',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontFamily: 'Omnes',
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  '${widget.members[0].name} will be added to your bubble once all members have accepted.',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleDecline(BuildContext context) {
    // Show a confirmation dialog before declining
    _showDeclineConfirmationDialog(context);
  }

  void _showDeclineConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder:
          (dialogContext) => AlertDialog(
            backgroundColor: const Color(0xFF1A2B4D),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            title: const Text(
              'Are you sure?',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            content: Text(
              '${widget.members[0].name} will not be able to join your bubble "${widget.bubbleName}".',
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            actionsAlignment: MainAxisAlignment.spaceEvenly,
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
                child: const Text(
                  'Let me rethink',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _submitDecline(context);
                },
                child: const Text(
                  'Confirm',
                  style: TextStyle(
                    color: Color(0xFF00FFFF),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  void _submitDecline(BuildContext context) {
    if (_isSubmitting) return;

    _bubbleProposeRequestBloc.add(
      DeclineBubbleProposeRequestEvent(
        bubbleId: widget.bubbleId,
        proposedMemberId: widget.proposedMemberId,
      ),
    );
    // Notify UnifiedProfileBloc
    context.read<UnifiedProfileBloc>().add(
      BubbleRequestDeclinedEvent(targetUserId: widget.proposedMemberId),
    );
  }
}
