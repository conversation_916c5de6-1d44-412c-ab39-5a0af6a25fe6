import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../statefulbusinesslogic/bloc/activity_status/activity_status_bloc.dart';
import '../../../statefulbusinesslogic/bloc/activity_status/activity_status_event.dart';
import '../../../statefulbusinesslogic/bloc/activity_status/activity_status_state.dart';
import '../../../statefulbusinesslogic/core/models/activity_status_model.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/core/services/activity_status_service.dart';
import '../../../statefulbusinesslogic/bloc/privacy_settings/privacy_settings_bloc.dart';
import '../../../statefulbusinesslogic/bloc/privacy_settings/privacy_settings_event.dart';
import '../../../statefulbusinesslogic/bloc/privacy_settings/privacy_settings_state.dart';
import '../../../statefulbusinesslogic/core/models/user_privacy_settings_model.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/gradient_background.dart';

class PrivacyPage extends StatefulWidget {
  const PrivacyPage({super.key});

  @override
  State<PrivacyPage> createState() => _PrivacyPageState();
}

class _PrivacyPageState extends State<PrivacyPage> {
  @override
  void initState() {
    super.initState();
    // Load activity status and privacy settings when page loads
    final authState = context.read<AuthBloc>().state;
    if (authState.status == AuthStatus.authenticated && authState.userId != null) {
      context.read<ActivityStatusBloc>().add(LoadActivityStatusEvent(userId: authState.userId!));
      context.read<PrivacySettingsBloc>().add(LoadPrivacySettings(authState.userId!));
    }
  }

  void _syncActivityStatusWithService(ActivityStatusModel activityStatus) {
    // Update the activity status service with the new settings
    ActivityStatusService.instance.updateActivityStatusSettings(activityStatus);
  }

  @override
  Widget build(BuildContext context) => GradientBackground(
    child: Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Privacy'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.only(
            bottom: kBottomNavigationBarHeight + 20.0,
          ),
          children: [
            // Account Privacy
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Account Privacy',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
            BlocConsumer<PrivacySettingsBloc, PrivacySettingsState>(
              listener: (context, state) {
                if (state is PrivacySettingsUpdated) {
                  if (state.message != null) {
                    CustomToast.showSuccess(context, state.message!);
                  }
                } else if (state is PrivacySettingsError) {
                  CustomToast.showError(context, state.message);
                }
              },
              builder: (context, state) {
                bool isLoading = state is PrivacySettingsLoading;
                bool isPrivateAccount = false;

                if (state is PrivacySettingsLoaded || state is PrivacySettingsUpdated) {
                  final settings = state is PrivacySettingsLoaded 
                      ? state.settings 
                      : (state as PrivacySettingsUpdated).settings;
                  isPrivateAccount = settings.whoCanSendContactRequests == ProfileFieldVisibility.contactsOnly;
                }

                return SwitchListTile(
                  title: const Text('Private Account'),
                  subtitle: const Text(
                    'When enabled, only your contacts can find you in search',
                  ),
                  value: isPrivateAccount,
                  onChanged: isLoading ? null : (value) {
                    final authState = context.read<AuthBloc>().state;
                    if (authState.status == AuthStatus.authenticated && authState.userId != null) {
                      context.read<PrivacySettingsBloc>().add(
                        UpdateAccountPrivacy(
                          userId: authState.userId!,
                          isPrivate: value,
                        ),
                      );
                    }
                  },
                );
              },
            ),
            const Divider(height: 32),

            // Activity Status
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Activity Status',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
            BlocConsumer<ActivityStatusBloc, ActivityStatusState>(
              listener: (context, state) {
                if (state is ActivityStatusLoaded) {
                  // Sync with service when loaded
                  _syncActivityStatusWithService(state.activityStatus);
                } else if (state is ActivityStatusUpdated) {
                  // Update the activity status service
                  _syncActivityStatusWithService(state.activityStatus);

                  // Show success message
                  if (state.message != null) {
                    CustomToast.showSuccess(context, state.message!);
                  }
                } else if (state is ActivityStatusError) {
                  // Show error message
                  CustomToast.showError(context, state.message);
                }
              },
              builder: (context, state) {
                bool isLoading = state is ActivityStatusLoading || state is ActivityStatusUpdating;
                bool currentValue = true; // Default value

                if (state is ActivityStatusLoaded) {
                  currentValue = state.activityStatus.showActivityStatus;
                } else if (state is ActivityStatusUpdated) {
                  currentValue = state.activityStatus.showActivityStatus;
                } else if (state is ActivityStatusUpdating) {
                  currentValue = state.currentStatus.showActivityStatus;
                }

                return SwitchListTile(
                  title: const Text('Show Activity Status'),
                  subtitle: const Text("Let others see when you're online"),
                  value: currentValue,
                  onChanged: isLoading ? null : (value) {
                    final authState = context.read<AuthBloc>().state;
                    if (authState.status == AuthStatus.authenticated && authState.userId != null) {
                      context.read<ActivityStatusBloc>().add(
                        UpdateActivityStatusEvent(
                          userId: authState.userId!,
                          showActivityStatus: value,
                        ),
                      );
                    }
                  },
                );
              },
            ),
            const Divider(height: 32),

            // Blocked Users
            BlocConsumer<PrivacySettingsBloc, PrivacySettingsState>(
              listener: (context, state) {
                if (state is UserBlocked) {
                  CustomToast.showSuccess(context, state.message);
                } else if (state is UserUnblocked) {
                  CustomToast.showSuccess(context, state.message);
                } else if (state is PrivacySettingsError) {
                  CustomToast.showError(context, state.message);
                }
              },
              builder: (context, state) {
                List<String> blockedUsers = [];
                
                if (state is PrivacySettingsLoaded) {
                  blockedUsers = state.blockedUsers;
                } else if (state is BlockedUsersLoaded) {
                  blockedUsers = state.blockedUsers;
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Blocked Users',
                            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          TextButton.icon(
                            onPressed: () => _showBlockUserDialog(context),
                            icon: const Icon(Icons.block),
                            label: const Text('Block User'),
                          ),
                        ],
                      ),
                    ),
                    if (blockedUsers.isEmpty)
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                        child: Card(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Icon(Icons.info_outline, color: Colors.grey),
                                SizedBox(width: 16),
                                Text(
                                  'No blocked users',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: blockedUsers.length,
                        itemBuilder: (context, index) => Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 4,
                          ),
                          child: ListTile(
                            leading: const CircleAvatar(
                              backgroundColor: Colors.grey,
                              child: Icon(Icons.person, color: Colors.white),
                            ),
                            title: Text(blockedUsers[index]),
                            subtitle: const Text('Blocked'),
                            trailing: TextButton.icon(
                              onPressed: () {
                                _showUnblockConfirmation(context, blockedUsers[index]);
                              },
                              icon: const Icon(Icons.remove_circle_outline),
                              label: const Text('Unblock'),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.red,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    ),
  );

  void _showBlockUserDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        final controller = TextEditingController();
        return AlertDialog(
          backgroundColor: const Color(0xFF1A2B4D),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          title: const Text(
            'Block user',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                "Blocked users won't be able to:",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '• Send you messages or calls',
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                '• Add you as a contact',
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                '• See your activity status',
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                '• Invite you to bubbles',
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter username',
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF00FFFF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  prefixIcon: const Icon(Icons.person, color: Colors.white),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  final authState = context.read<AuthBloc>().state;
                  if (authState.status == AuthStatus.authenticated && authState.userId != null) {
                    context.read<PrivacySettingsBloc>().add(
                      BlockUser(
                        userId: authState.userId!,
                        targetUserId: controller.text,
                      ),
                    );
                  }
                  Navigator.pop(dialogContext);
                }
              },
              child: const Text('Block', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showUnblockConfirmation(BuildContext context, String targetUserId) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Unblock User'),
        content: Text('Are you sure you want to unblock $targetUserId?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final authState = context.read<AuthBloc>().state;
              if (authState.status == AuthStatus.authenticated && authState.userId != null) {
                context.read<PrivacySettingsBloc>().add(
                  UnblockUser(
                    userId: authState.userId!,
                    targetUserId: targetUserId,
                  ),
                );
              }
              Navigator.pop(dialogContext);
            },
            child: const Text('Unblock'),
          ),
        ],
      ),
    );
  }
}
