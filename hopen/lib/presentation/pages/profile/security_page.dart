import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/profile_option_tile.dart';

/// Displays security-related settings for the user account.
///
/// Currently includes options for password reset and potentially
/// future security checks.
class SecurityPage extends StatelessWidget {
  /// Constructs a [SecurityPage].
  const SecurityPage({super.key});

  @override
  Widget build(BuildContext context) {
    /// Common trailing widget for navigation tiles in profile sections.
    const trailingArrow = Icon(
      Icons.arrow_forward_ios,
      size: 16,
      color: Colors.white70,
    );

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Security'),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ListView(
              children: [
                const SizedBox(height: 16),
                // --- Reset Password Option ---
                ProfileOptionTile(
                  title: 'Reset password',
                  subtitle: 'Send a password reset link to your email',
                  iconWidget: const Icon(Icons.lock_reset, color: Colors.white),
                  trailing: trailingArrow,
                  onTap: () => _showPasswordResetDialog(context),
                ),
                // --- Verify Password Security Option ---
                ProfileOptionTile(
                  title: 'Check password security',
                  subtitle: 'Check if your password has been compromised',
                  iconWidget: const Icon(
                    Icons.security_update_warning_outlined,
                    color: Colors.white,
                  ),
                  trailing: trailingArrow,
                  onTap: () => _showPasswordCheckDialog(context),
                ),
                // Future security options (e.g., 2FA, Login History) can be added here.
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Displays a confirmation dialog for the password reset action.
  void _showPasswordResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            // TODO: Consider custom dialog styling to match app theme.
            title: const Text('Reset Password'),
            content: const Text(
              'A password reset link will be sent to your email address.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => _initiatePasswordReset(context),
                child: const Text('Send reset link'),
              ),
            ],
          ),
    );
  }

  /// Initiate password reset through auth service
  void _initiatePasswordReset(BuildContext context) {
    Navigator.pop(context); // Close dialog first

    // Get the current auth state to access user email
    final authBloc = context.read<AuthBloc>();
    final currentState = authBloc.state;

    // Check if user is authenticated and has email
    if (currentState.status != AuthStatus.authenticated ||
        currentState.email == null) {
      CustomToast.showError(
        context,
        'Unable to get user information. Please try again.',
      );
      return;
    }

    final userEmail = currentState.email!;

    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // Use auth bloc to initiate password reset
    authBloc.add(SendPasswordResetEmailEvent(email: userEmail));

    // Note: In a production app, you should use BlocListener in the widget tree
    // to handle state changes. For this simple case, we'll show a success message
    // and let the user check their email.

    // Hide loading after a short delay and show success message
    Future.delayed(const Duration(seconds: 1), () {
      Navigator.pop(context); // Hide loading
      CustomToast.showSuccess(
        context,
        'Password reset link sent to $userEmail',
      );
    });
  }

  /// Show password check dialog with current password verification
  void _showPasswordCheckDialog(BuildContext context) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Verify Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter your current password to check if it has been compromised.',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Current Password',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => _checkPassword(context, passwordController.text),
            child: const Text('Check'),
          ),
        ],
      ),
    );
  }

  /// Check if password has been compromised
  Future<void> _checkPassword(BuildContext context, String password) async {
    Navigator.pop(context); // Close dialog first

    if (password.isEmpty) {
      CustomToast.showError(context, 'Please enter your password');
      return;
    }

    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Simulate password check (in real implementation, you'd use a service like HaveIBeenPwned)
      await Future.delayed(const Duration(seconds: 2));

      Navigator.pop(context); // Hide loading

      // For demo purposes, show a random result
      final isCompromised = DateTime.now().millisecond % 2 == 0;

      if (isCompromised) {
        CustomToast.showError(
          context,
          'Your password has been found in data breaches. Consider changing it.',
        );
      } else {
        CustomToast.showSuccess(
          context,
          'Your password appears to be secure.',
        );
      }
    } catch (e) {
      Navigator.pop(context); // Hide loading if still showing
      CustomToast.showError(
        context,
        'Failed to check password: $e',
      );
    }
  }
}
