import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../di/injection_container_refactored.dart' as di;
import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/profile_option_tile.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  bool _locationSharingEnabled = false;
  bool _biometricEnabled = false;
  bool _twoFactorEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      setState(() {
        _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
        _darkModeEnabled = prefs.getBool('dark_mode_enabled') ?? false;
        _locationSharingEnabled = prefs.getBool('location_sharing_enabled') ?? false;
        _biometricEnabled = prefs.getBool('biometric_enabled') ?? false;
        _twoFactorEnabled = prefs.getBool('two_factor_enabled') ?? false;
      });
    } catch (e) {
      LoggingService.error('Failed to load settings: $e');
      // Use default values if loading fails
      setState(() {
        _notificationsEnabled = true;
        _darkModeEnabled = false;
        _locationSharingEnabled = false;
        _biometricEnabled = false;
        _twoFactorEnabled = false;
      });
    }
  }

  Future<void> _showLogoutConfirmation() async {
    final shouldLogout = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ],
          ),
    );

    if (shouldLogout == true && mounted) {
      _performLogout();
    }
  }

  void _performLogout() {
    // Clear user data and logout
    context.read<AuthBloc>().add(const LogoutEvent());

    // Navigate to login screen
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _showDeleteAccountConfirmation() async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            shape: RoundedSuperellipseBorder(
              borderRadius: BorderRadius.circular(18),
            ),
            title: const Text('Delete your account'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'This action is irreversible. Your account and all associated data will be permanently deleted.',
                ),
                SizedBox(height: 16),
                Text(
                  'This includes:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('• Your profile and settings'),
                Text('• All your messages and media'),
                Text('• Your bubble history'),
                Text('• Your contacts and connections'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete your account'),
              ),
            ],
          ),
    );

    if (shouldDelete == true && mounted) {
      await _deleteAccount();
    }
  }

  Future<void> _changePassword() async {
    // Navigate to change password screen
    Navigator.of(context).pushNamed('/change-password');
  }

  Future<void> _manageTwoFactor() async {
    // Navigate to two-factor authentication setup
    Navigator.of(context).pushNamed('/two-factor');
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
      Card(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(children: children),
      ),
      const SizedBox(height: 16),
    ],
  );

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    IconData? icon,
  }) => ListTile(
    leading: icon != null ? Icon(icon) : null,
    title: Text(title),
    subtitle: Text(subtitle),
    trailing: Switch(value: value, onChanged: onChanged),
  );

  Widget _buildActionTile({
    required String title,
    required VoidCallback onTap,
    IconData? icon,
    String? subtitle,
    Color? textColor,
    Widget? trailing,
  }) => ListTile(
    leading: icon != null ? Icon(icon, color: textColor) : null,
    title: Text(title, style: TextStyle(color: textColor)),
    subtitle: subtitle != null ? Text(subtitle) : null,
    trailing: trailing ?? const Icon(Icons.chevron_right),
    onTap: onTap,
  );

  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Settings'), elevation: 0),
    body: SafeArea(
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),

            // Notifications Section
            _buildSettingsSection(
              title: 'Notifications',
              children: [
                _buildSwitchTile(
                  title: 'Push Notifications',
                  subtitle: 'Receive notifications for messages and calls',
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() => _notificationsEnabled = value);
                    _saveSetting('notifications_enabled', value);
                  },
                  icon: Icons.notifications,
                ),
              ],
            ),

            // Appearance Section
            _buildSettingsSection(
              title: 'Appearance',
              children: [
                _buildSwitchTile(
                  title: 'Dark Mode',
                  subtitle: 'Use dark theme',
                  value: _darkModeEnabled,
                  onChanged: (value) {
                    setState(() => _darkModeEnabled = value);
                    _saveSetting('dark_mode_enabled', value);
                  },
                  icon: Icons.dark_mode,
                ),
              ],
            ),

            // Privacy Section
            _buildSettingsSection(
              title: 'Privacy & Security',
              children: [
                _buildSwitchTile(
                  title: 'Location Sharing',
                  subtitle: 'Share your location with friends',
                  value: _locationSharingEnabled,
                  onChanged: (value) {
                    setState(() => _locationSharingEnabled = value);
                    _saveSetting('location_sharing_enabled', value);
                  },
                  icon: Icons.location_on,
                ),
                _buildSwitchTile(
                  title: 'Biometric Authentication',
                  subtitle: 'Use fingerprint or face ID',
                  value: _biometricEnabled,
                  onChanged: (value) {
                    setState(() => _biometricEnabled = value);
                    _saveSetting('biometric_enabled', value);
                  },
                  icon: Icons.fingerprint,
                ),
                _buildActionTile(
                  title: 'Change Password',
                  subtitle: 'Update your account password',
                  icon: Icons.lock,
                  onTap: _changePassword,
                ),
                _buildActionTile(
                  title: 'Two-Factor Authentication',
                  subtitle: _twoFactorEnabled ? 'Enabled' : 'Disabled',
                  icon: Icons.security,
                  onTap: _manageTwoFactor,
                ),
              ],
            ),

            // Account Section
            _buildSettingsSection(
              title: 'Account',
              children: [
                _buildActionTile(
                  title: 'Logout',
                  subtitle: 'Sign out of your account',
                  icon: Icons.logout,
                  textColor: Colors.orange,
                  onTap: _showLogoutConfirmation,
                ),
                _buildActionTile(
                  title: 'Delete your account',
                  subtitle: 'Permanently delete your account',
                  icon: Icons.delete_forever,
                  textColor: Colors.red,
                  onTap: _showDeleteAccountConfirmation,
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    ),
  );

  /// Save a setting to SharedPreferences
  Future<void> _saveSetting(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(key, value);
    } catch (e) {
      LoggingService.error('Failed to save setting $key: $e');
    }
  }

  /// Delete user account
  Future<void> _deleteAccount() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Get auth bloc and attempt account deletion
      final authBloc = context.read<AuthBloc>();

      // Check if user is authenticated from current state
      if (authBloc.state.status != AuthStatus.authenticated) {
        throw Exception('User not authenticated');
      }

      // In a real implementation, you would call a delete account API
      // For now, we'll simulate the process and sign out the user
      await Future.delayed(const Duration(seconds: 2));

      // Sign out the user using AuthBloc
      authBloc.add(const LogoutEvent());

      // Wait for logout to complete
      await for (final state in authBloc.stream) {
        if (state.status == AuthStatus.unauthenticated) {
          if (mounted) {
            Navigator.pop(context); // Hide loading dialog

            // Clear all stored settings
            final prefs = await SharedPreferences.getInstance();
            await prefs.clear();

            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Account deletion initiated. You have been signed out.'),
                backgroundColor: Colors.green,
              ),
            );

            // Navigate to login screen
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/login',
              (route) => false,
            );
          }
          break;
        } else if (state.status == AuthStatus.error) {
          throw Exception('Failed to sign out after account deletion');
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Hide loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
