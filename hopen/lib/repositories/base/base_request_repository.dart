import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/error/request_error_handler.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';

/// Base repository class for all request types
/// Provides standardized patterns for contact, bubble, and friend requests
abstract class BaseRequestRepository<T> {
  const BaseRequestRepository();

  /// Get pending requests for a user
  Future<Result<List<T>>> getPendingRequests(String userId);

  /// Accept a request
  Future<Result<void>> acceptRequest(String requestId);

  /// Decline a request
  Future<Result<void>> declineRequest(String requestId);

  /// Get a specific request by ID
  Future<Result<T>> getRequest(String requestId);

  /// Helper method to handle repository operations with standardized error handling
  Future<Result<R>> handleRepositoryOperation<R>(
    String operation,
    Future<R> Function() repositoryCall, {
    Map<String, dynamic>? validationParams,
  }) async {
    try {
      // Validate parameters if provided
      if (validationParams != null) {
        final validationResult = RequestErrorHandler.validateRequestParameters(
          operation: operation,
          parameters: validationParams,
        );
        
        if (validationResult.isFailure) {
          return Result.failure(validationResult.error!);
        }
      }

      LoggingService.info('BaseRequestRepository: Starting $operation');
      
      final result = await repositoryCall();
      
      LoggingService.info('BaseRequestRepository: $operation completed successfully');
      return Result.success(result);
      
    } catch (error) {
      LoggingService.error('BaseRequestRepository: $operation failed with error: $error');
      return RequestErrorHandler.handleHttpError<R>(error, operation);
    }
  }

  /// Helper method for operations that return void
  Future<Result<void>> handleVoidRepositoryOperation(
    String operation,
    Future<void> Function() repositoryCall, {
    Map<String, dynamic>? validationParams,
  }) async {
    return handleRepositoryOperation<void>(
      operation,
      repositoryCall,
      validationParams: validationParams,
    );
  }

  /// Helper method for operations that return lists
  Future<Result<List<R>>> handleListRepositoryOperation<R>(
    String operation,
    Future<List<R>> Function() repositoryCall, {
    Map<String, dynamic>? validationParams,
  }) async {
    return handleRepositoryOperation<List<R>>(
      operation,
      repositoryCall,
      validationParams: validationParams,
    );
  }

  /// Helper method to validate common request parameters
  Map<String, dynamic> createValidationParams({
    String? requestId,
    String? userId,
    String? targetUserId,
    String? bubbleId,
  }) {
    final params = <String, dynamic>{};
    
    if (requestId != null) params['requestId'] = requestId;
    if (userId != null) params['userId'] = userId;
    if (targetUserId != null) params['targetUserId'] = targetUserId;
    if (bubbleId != null) params['bubbleId'] = bubbleId;
    
    return params;
  }

  /// Log repository operation for debugging
  void logOperation(String operation, Map<String, dynamic> params) {
    LoggingService.info(
      'BaseRequestRepository: $operation called with params: $params'
    );
  }
}

/// Mixin for repositories that need to send requests
mixin RequestSenderMixin<T> on BaseRequestRepository<T> {
  /// Send a request
  Future<Result<T>> sendRequest({
    required String targetUserId,
    required String message,
    Map<String, dynamic>? additionalParams,
  });

  /// Helper method for sending requests with validation
  Future<Result<R>> handleSendRequest<R>(
    String operation,
    Future<R> Function() sendCall, {
    required String targetUserId,
    String? message,
    Map<String, dynamic>? additionalParams,
  }) async {
    final validationParams = {
      'targetUserId': targetUserId,
      if (message != null && message.isNotEmpty) 'message': message,
      ...?additionalParams,
    };

    return handleRepositoryOperation<R>(
      operation,
      sendCall,
      validationParams: validationParams,
    );
  }
}

/// Mixin for repositories that handle bubble-specific operations
mixin BubbleRequestMixin<T> on BaseRequestRepository<T> {
  /// Get requests for a specific bubble
  Future<Result<List<T>>> getBubbleRequests(String bubbleId);

  /// Helper method for bubble operations
  Future<Result<R>> handleBubbleOperation<R>(
    String operation,
    Future<R> Function() bubbleCall, {
    required String bubbleId,
    String? userId,
    Map<String, dynamic>? additionalParams,
  }) async {
    final validationParams = {
      'bubbleId': bubbleId,
      if (userId != null) 'userId': userId,
      ...?additionalParams,
    };

    return handleRepositoryOperation<R>(
      operation,
      bubbleCall,
      validationParams: validationParams,
    );
  }
}

/// Mixin for repositories that need rate limiting awareness
mixin RateLimitAwareMixin<T> on BaseRequestRepository<T> {
  /// Check if operation is rate limited
  bool isRateLimited(String operation, String userId) {
    // This would integrate with your rate limiting service
    // For now, return false as a placeholder
    return false;
  }

  /// Helper method that includes rate limit checking
  Future<Result<R>> handleRateLimitedOperation<R>(
    String operation,
    String userId,
    Future<R> Function() operationCall, {
    Map<String, dynamic>? validationParams,
  }) async {
    if (isRateLimited(operation, userId)) {
      return RequestErrorHandler.handleHttpError<R>(
        'Rate limit exceeded for $operation',
        operation,
      );
    }

    return handleRepositoryOperation<R>(
      operation,
      operationCall,
      validationParams: validationParams,
    );
  }
}
