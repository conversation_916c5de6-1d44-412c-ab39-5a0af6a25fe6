import 'package:hopen/statefulbusinesslogic/core/models/user_privacy_settings_model.dart';
import 'package:hopen/statefulbusinesslogic/core/error/failures.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';

/// Repository interface for privacy settings management
/// Follows the clean architecture pattern - domain layer
abstract class PrivacySettingsRepository {
  /// Load privacy settings for a user
  Future<Result<UserPrivacySettingsModel>> loadPrivacySettings(String userId);
  
  /// Update privacy settings for a user
  Future<Result<UserPrivacySettingsModel>> updatePrivacySettings(UserPrivacySettingsModel settings);
  
  /// Get blocked users list for a user
  Future<Result<List<String>>> getBlockedUsers(String userId);
  
  /// Block a user
  Future<Result<bool>> blockUser({required String userId, required String targetUserId});
  
  /// Unblock a user
  Future<Result<bool>> unblockUser({required String userId, required String targetUserId});
} 